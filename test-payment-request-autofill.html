<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تعبئة البيانات البنكية التلقائية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }
        .content {
            padding: 40px;
        }
        .task-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .payment-form {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-test {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }
        .recipient-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }
        .bank-details-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .auto-filled {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        .info-value {
            color: #007bff;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="bi bi-credit-card me-3"></i>اختبار تعبئة البيانات البنكية التلقائية</h1>
            <p class="mb-0">اختبار تعبئة البيانات البنكية تلقائياً عند اختيار المستلم في طلب السحب النقدي</p>
        </div>
        
        <div class="content">
            <!-- Task Information -->
            <div class="task-info">
                <h4><i class="bi bi-info-circle me-2"></i>معلومات المهمة</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">رقم المهمة:</span>
                            <span class="info-value">#1001</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">مبلغ السائق:</span>
                            <span class="info-value">850.00 ريال</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">اسم السائق:</span>
                            <span class="info-value">محمد أحمد السائق</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <span class="info-label">قائد الفريق:</span>
                            <span class="info-value">علي حسن القائد</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">من:</span>
                            <span class="info-value">الرياض - حي النخيل</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">إلى:</span>
                            <span class="info-value">جدة - حي الصفا</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Request Form -->
            <div class="payment-form">
                <h4><i class="bi bi-wallet2 me-2"></i>نموذج طلب السحب النقدي</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المطلوب *</label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control" id="requestedAmount" 
                                    placeholder="0.00" max="850">
                                <span class="input-group-text">ريال</span>
                            </div>
                            <div class="form-text">
                                <small class="text-muted">الحد الأقصى: <span class="text-primary fw-bold">850.00 ريال</span></small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المستلم *</label>
                            <select class="form-select" id="paymentRecipient">
                                <option value="">اختر المستلم</option>
                                <option value="driver">السائق</option>
                                <option value="team_leader">قائد الفريق</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Recipient Info Display -->
                <div id="driverInfo" class="recipient-info">
                    <h6><i class="bi bi-truck me-2"></i>معلومات السائق</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>الاسم:</strong> محمد أحمد السائق
                        </div>
                        <div class="col-md-4">
                            <strong>الهاتف:</strong> +************
                        </div>
                        <div class="col-md-4">
                            <strong>البنك:</strong> بنك الراجحي
                        </div>
                    </div>
                </div>

                <div id="teamLeaderInfo" class="recipient-info">
                    <h6><i class="bi bi-person-badge me-2"></i>معلومات قائد الفريق</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>الاسم:</strong> علي حسن القائد
                        </div>
                        <div class="col-md-4">
                            <strong>الهاتف:</strong> +************
                        </div>
                        <div class="col-md-4">
                            <strong>البنك:</strong> البنك الأهلي السعودي
                        </div>
                    </div>
                </div>

                <!-- Bank Details Section -->
                <div class="bank-details-section">
                    <h5><i class="bi bi-building-bank me-2"></i>البيانات البنكية</h5>
                    <div class="alert alert-info alert-custom">
                        <i class="bi bi-lightbulb me-2"></i>
                        <strong>ملاحظة:</strong> سيتم تعبئة البيانات البنكية تلقائياً عند اختيار المستلم
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اسم البنك *</label>
                                <select class="form-select" id="bankName">
                                    <option value="">اختر البنك</option>
                                    <option value="البنك الأهلي السعودي">البنك الأهلي السعودي</option>
                                    <option value="بنك الراجحي">بنك الراجحي</option>
                                    <option value="بنك الرياض">بنك الرياض</option>
                                    <option value="البنك السعودي للاستثمار">البنك السعودي للاستثمار</option>
                                    <option value="البنك السعودي الفرنسي">البنك السعودي الفرنسي</option>
                                    <option value="البنك العربي الوطني">البنك العربي الوطني</option>
                                    <option value="بنك ساب">بنك ساب</option>
                                    <option value="بنك الجزيرة">بنك الجزيرة</option>
                                    <option value="البنك السعودي البريطاني">البنك السعودي البريطاني</option>
                                    <option value="بنك الإنماء">بنك الإنماء</option>
                                    <option value="other">أخرى</option>
                                </select>
                                <input type="text" class="form-control mt-2" id="customBankName" 
                                    placeholder="أدخل اسم البنك" style="display: none;">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الحساب *</label>
                                <input type="text" class="form-control" id="accountNumber" 
                                    placeholder="**********" minlength="8" maxlength="20">
                                <div class="form-text">
                                    <small class="text-muted">أرقام فقط، 8-20 رقم</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الآيبان *</label>
                                <input type="text" class="form-control" id="ibanNumber" 
                                    placeholder="SA12 3456 7890 1234 5678 90" maxlength="29">
                                <div class="form-text">
                                    <small class="text-muted">التنسيق: SA + 22 رقم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Button -->
                <div class="text-center mt-4">
                    <button type="button" class="btn btn-test btn-lg" onclick="testPaymentRequest()">
                        <i class="bi bi-check-circle me-2"></i>اختبار طلب السحب
                    </button>
                </div>
            </div>

            <!-- Instructions -->
            <div class="alert alert-success alert-custom mt-4">
                <h5><i class="bi bi-info-circle me-2"></i>تعليمات الاختبار:</h5>
                <ul class="mb-0">
                    <li>اختر المستلم (السائق أو قائد الفريق)</li>
                    <li>ستلاحظ تعبئة البيانات البنكية تلقائياً</li>
                    <li>الحقول المعبأة تلقائياً ستظهر بلون أخضر فاتح</li>
                    <li>يمكنك تعديل البيانات المعبأة حسب الحاجة</li>
                    <li>أدخل المبلغ المطلوب واضغط "اختبار طلب السحب"</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Sample task data with bank details
        const taskData = {
            id: 1001,
            driver_name: 'محمد أحمد السائق',
            driver_phone: '+************',
            driver_bank_name: 'بنك الراجحي',
            driver_account_number: '**********123456',
            driver_iban_number: 'SA********************12',
            team_leader_name: 'علي حسن القائد',
            team_leader_phone: '+************',
            team_leader_bank_name: 'البنك الأهلي السعودي',
            team_leader_account_number: '****************',
            team_leader_iban_number: 'SA****************321098'
        };

        $(document).ready(function() {
            // Handle payment recipient change
            $('#paymentRecipient').on('change', function() {
                const recipient = $(this).val();
                
                // Hide all recipient info
                $('.recipient-info').hide();
                
                // Clear current bank details and remove auto-filled styling
                $('#bankName').val('').removeClass('auto-filled');
                $('#customBankName').val('').hide().removeClass('auto-filled');
                $('#accountNumber').val('').removeClass('auto-filled');
                $('#ibanNumber').val('').removeClass('auto-filled');

                if (recipient === 'driver') {
                    // Show driver info
                    $('#driverInfo').show();
                    
                    // Fill driver bank details
                    if (taskData.driver_bank_name) {
                        const bankOption = $('#bankName option[value="' + taskData.driver_bank_name + '"]');
                        if (bankOption.length > 0) {
                            $('#bankName').val(taskData.driver_bank_name).addClass('auto-filled');
                        } else {
                            $('#bankName').val('other').addClass('auto-filled');
                            $('#customBankName').val(taskData.driver_bank_name).show().addClass('auto-filled');
                        }
                    }
                    
                    if (taskData.driver_account_number) {
                        $('#accountNumber').val(taskData.driver_account_number).addClass('auto-filled');
                    }
                    
                    if (taskData.driver_iban_number) {
                        const formattedIban = taskData.driver_iban_number.replace(/(.{4})/g, '$1 ').trim();
                        $('#ibanNumber').val(formattedIban).addClass('auto-filled');
                    }
                    
                } else if (recipient === 'team_leader') {
                    // Show team leader info
                    $('#teamLeaderInfo').show();
                    
                    // Fill team leader bank details
                    if (taskData.team_leader_bank_name) {
                        const bankOption = $('#bankName option[value="' + taskData.team_leader_bank_name + '"]');
                        if (bankOption.length > 0) {
                            $('#bankName').val(taskData.team_leader_bank_name).addClass('auto-filled');
                        } else {
                            $('#bankName').val('other').addClass('auto-filled');
                            $('#customBankName').val(taskData.team_leader_bank_name).show().addClass('auto-filled');
                        }
                    }
                    
                    if (taskData.team_leader_account_number) {
                        $('#accountNumber').val(taskData.team_leader_account_number).addClass('auto-filled');
                    }
                    
                    if (taskData.team_leader_iban_number) {
                        const formattedIban = taskData.team_leader_iban_number.replace(/(.{4})/g, '$1 ').trim();
                        $('#ibanNumber').val(formattedIban).addClass('auto-filled');
                    }
                }
            });

            // Handle bank selection
            $('#bankName').on('change', function() {
                const selectedValue = $(this).val();
                if (selectedValue === 'other') {
                    $('#customBankName').show();
                } else {
                    $('#customBankName').hide().val('');
                }
            });

            // Format account number (numbers only)
            $('#accountNumber').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
                // Remove auto-filled styling when user modifies
                $(this).removeClass('auto-filled');
            });

            // Format IBAN number
            $('#ibanNumber').on('input', function() {
                let value = $(this).val().replace(/\s/g, '').toUpperCase();
                // Ensure it starts with SA
                if (value && !value.startsWith('SA')) {
                    value = 'SA' + value.replace(/^SA/i, '');
                }
                let formatted = value.replace(/(.{4})/g, '$1 ').trim();
                $(this).val(formatted);
                // Remove auto-filled styling when user modifies
                $(this).removeClass('auto-filled');
            });

            // Remove auto-filled styling when user modifies bank selection
            $('#bankName, #customBankName').on('change input', function() {
                $(this).removeClass('auto-filled');
            });
        });

        function testPaymentRequest() {
            const requestedAmount = $('#requestedAmount').val();
            const recipient = $('#paymentRecipient').val();
            const bankName = $('#bankName').val() === 'other' ? $('#customBankName').val() : $('#bankName').val();
            const accountNumber = $('#accountNumber').val();
            const ibanNumber = $('#ibanNumber').val();

            if (!requestedAmount || !recipient || !bankName || !accountNumber || !ibanNumber) {
                alert('يرجى تعبئة جميع الحقول المطلوبة');
                return;
            }

            const recipientName = recipient === 'driver' ? taskData.driver_name : taskData.team_leader_name;
            
            alert(`تم إنشاء طلب السحب بنجاح!\n\nالمبلغ: ${requestedAmount} ريال\nالمستلم: ${recipientName}\nالبنك: ${bankName}\nرقم الحساب: ${accountNumber}\nالآيبان: ${ibanNumber}`);
        }
    </script>
</body>
</html>

{"Dashboards": "Dashboards", "Dashboard": "Dashboard", "eCommerce": "eCommerce", "CRM": "CRM", "Layouts": "Layouts", "Collapsed menu": "Collapsed menu", "Content navbar": "Content navbar", "Content nav + Sidebar": "Content nav + Sidebar", "Horizontal": "Horizontal", "Vertical": "Vertical", "Without menu": "Without menu", "Without navbar": "Without navbar", "Fluid": "Fluid", "Container": "Container", "Blank": "Blank", "Laravel Example": "<PERSON><PERSON> Example", "User Management": "User Management", "Apps": "Apps", "Email": "Email", "Chat": "Cha<PERSON>", "Calendar": "Calendar", "Kanban": "Ka<PERSON><PERSON>", "Products": "Products", "Add Product": "Add Product", "Product List": "Product List", "Category List": "Category List", "Category": "Category", "Order": "Order", "Order List": "Order List", "Order Details": "Order Details", "Customer": "Customer", "All Customer": "All Customer", "All Customers": "All Customers", "Customer Details": "Customer Details", "Overview": "Overview", "Address & Billing": "Address & Billing", "Manage Reviews": "Manage Reviews", "Referrals": "Referrals", "Settings": "Settings", "Store Details": "Store Details", "Payments": "Payments", "Shipping & Delivery": "Shipping & Delivery", "Locations": "Locations", "Roles & Permissions": "Roles & Permissions", "Add new roles with customized permissions as per your requirement": "Add new roles with customized permissions as per your requirement", "Add New Role": "Add New Role", "Edit Role": "Edit Role", "Role": "Role", "Created At": "Created At", "Actions": "Actions", "Search User": "Search User", "Displaying _START_ to _END_ of _TOTAL_ entries": "Displaying _START_ to _END_ of _TOTAL_ entries", "Showing _START_ to _END_ of _TOTAL_ entries": "Showing _START_ to _END_ of _TOTAL_ entries", "Search...": "Search...", "add new role": "add new role", "role": "role", "role name": "role name", "guard": "guard", "Administrator": "Administrator", "Driver": "Driver", "Details of": "Details of", "There is no Permissions found!": "There is no Permissions found!", "Error!! can not fiche any Permission": "Error!! can not fiche any Permission", "permissions": "permissions", "Roles": "Roles", "Role Name": "Role Name", "Guard": "Guard", "Permissions": "Permissions", "Home": "Home", "Profile": "Profile", "Messages": "Messages", "Close": "Close", "Submit": "Submit", "Users": "Users", "Active Users": "Active Users", "Inactive Users": "Inactive Users", "Pending Users": "Pending Users", "Add New User": "Add New User", "User": "User", "Phone": "Phone", "Status": "Status", "Reset Password": "Reset Password", "Add new User": "Add new User", "Edit User": "Edit User", "Main": "Main", "Additional": "Additional", "Full Name": "Full Name", "<EMAIL>": "<EMAIL>", "Enter phone number": "Enter phone number", "Password": "Password", "Confirm Password": "Confirm Password", "User Role": "User Role", "Teams": "Teams", "Customers": "Customers", "Active Customers": "Active Customers", "Unverified Customers": "Unverified Customers", "Blocked Customers": "Blocked Customers", "name": "name", "email": "email", "phone": "phone", "tags": "tags", "status": "status", "created at": "created at", "actions": "actions", "Add New Customer": "Add New Customer", "Customer Role": "Customer Role", "Select Role": "Select Role", "Company Info": "Company Info", "Company Name": "Company Name", "enter company name": "enter company name", "Company Address": "Company Address", "enter company address": "enter company address", "Tags": "Tags", "Select Template": "Select Template", "-- Select Template": "-- Select Template", "--- Select Template": "--- Select Template", "Logistics": "Logistics", "Fleet": "Fleet", "Invoice": "Invoice", "Preview": "Preview", "Add": "Add", "Pages": "Pages", "User Profile": "User Profile", "Projects": "Projects", "Account Settings": "Account <PERSON><PERSON>", "Account": "Account", "Security": "Security", "Billing & Plans": "Billing & Plans", "Notifications": "Notifications", "Connections": "Connections", "FAQ": "FAQ", "Front Pages": "Front Pages", "Payment": "Payment", "Help Center": "Help Center", "Landing": "Landing", "Categories": "Categories", "Article": "Article", "Pricing": "Pricing", "Error": "Error", "Coming Soon": "Coming Soon", "Under Maintenance": "Under Maintenance", "Not Authorized": "Not Authorized", "Authentications": "Authentications", "Login": "<PERSON><PERSON>", "Register": "Register", "Verify Email": "<PERSON><PERSON><PERSON>", "Forgot Password": "Forgot Password", "Two Steps": "Two Steps", "Basic": "Basic", "Cover": "Cover", "Multi-steps": "Multi-steps", "Modal Examples": "Modal Examples", "Wizard Examples": "Wizard Examples", "Checkout": "Checkout", "Property Listing": "Property Listing", "Create Deal": "Create Deal", "Icons": "Icons", "Tabler": "Tabler", "Fontawesome": "Fontawesome", "User interface": "User interface", "Accordion": "Accordion", "Alerts": "<PERSON><PERSON><PERSON>", "App Brand": "App Brand", "Badges": "Badges", "Buttons": "Buttons", "Cards": "Cards", "Advance": "Advance", "Statistics": "Statistics", "Analytics": "Analytics", "Carousel": "Carousel", "Collapse": "Collapse", "Dropdowns": "Dropdowns", "Footer": "Footer", "List Groups": "List Groups", "Modals": "Modals", "Menu": "<PERSON><PERSON>", "Navbar": "<PERSON><PERSON><PERSON>", "Offcanvas": "<PERSON><PERSON><PERSON>", "Pagination & Breadcrumbs": "Pagination & Breadcrumbs", "Progress": "Progress", "Spinners": "Spinners", "Tabs & Pills": "Tabs & Pills", "Toasts": "Toasts", "Tooltips & Popovers": "Tooltips & Popovers", "Typography": "Typography", "Extended UI": "Extended UI", "Avatar": "Avatar", "BlockUI": "BlockUI", "Drag & Drop": "Drag & Drop", "Media Player": "Media Player", "Perfect Scrollbar": "Perfect Scrollbar", "Star Ratings": "Star Ratings", "SweetAlert2": "SweetAlert2", "Text Divider": "Text Divider", "Timeline": "Timeline", "Fullscreen": "Fullscreen", "Tour": "Tour", "Treeview": "Treeview", "Miscellaneous": "Miscellaneous", "Misc": "Misc", "Form Elements": "Form Elements", "Basic Inputs": "Basic Inputs", "Input groups": "Input groups", "Custom Options": "Custom Options", "Editors": "Editors", "File Upload": "File Upload", "Pickers": "Pickers", "Select & Tags": "Select & Tags", "Sliders": "Sliders", "Switches": "Switches", "Extras": "Extras", "Form Layouts": "Form Layouts", "Vertical Form": "Vertical Form", "Horizontal Form": "Horizontal Form", "Sticky Actions": "Sticky Actions", "Form Wizard": "Form Wizard", "Numbered": "Numbered", "Advanced": "Advanced", "Forms": "Forms", "Form Validation": "Form Validation", "Tables": "Tables", "Datatables": "Datatables", "Extensions": "Extensions", "Charts": "Charts", "Apex Charts": "Apex Charts", "ChartJS": "ChartJS", "Leaflet Maps": "Leaflet Maps", "Support": "Support", "Documentation": "Documentation", "Academy": "Academy", "My Course": "My Course", "Course Details": "Course Details", "Apps & Pages": "Apps & Pages", "Components": "Components", "Forms & Tables": "Forms & Tables", "Charts & Maps": "Charts & Maps", "Id": "Id", "General": "General", "Template": "Template", "Donut dragée jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.": "Donut dragée jelly pie halvah. Danish gingerbread bonbon cookie wafer candy oat cake ice cream. Gummies halvah tootsie roll muffin biscuit icing dessert gingerbread. Pastry ice cream cheesecake fruitcake.", "Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.": "Jelly-o jelly beans icing pastry cake cake lemon drops. Muffin muffin pie tiramisu halvah cotton candy liquorice caramels.", "Geo-fence": "Geo-fence", "It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.": "It allows you to categorize Manager and simplifies the process of task assignment by letting you create virtual boundaries.", "Add New Geo-fence": "Add New Geo-fence", "🔍 Search Team": "🔍 Search Team", "Geofences": "Geofences", "Add Geo-fence": "Add Geo-fence", "Select Teams": "Select Teams", "Name": "Name", "Enter name": "Enter name", "Description": "Description", "Enter description": "Enter description", "The role name is required.": "The role name is required.", "The role name has already been taken.": "The role name has already been taken.", "The guard field is required.": "The guard field is required.", "The selected guard is invalid.": "The selected guard is invalid.", "At least one permission must be selected.": "At least one permission must be selected.", "Permissions must be an array.": "Permissions must be an array.", "Error to Save Role": "Error to Save Role", "Role Saved": "Role Saved", "This role can not be deleted": "This role can not be deleted", "There are users connected with this role": "There are users connected with this role", "Error to delete role": "Error to delete role", "Role deleted": "Role deleted", "Rate": "Rate", "Fixed": "Fixed", "Commission Rate": "Commission Rate", "Commission fixed Amount": "Commission fixed Amount", "General Settings": "General Settings", "You can manage the main and vital settings of the platform from here, so be careful.": "You can manage the main and vital settings of the platform from here, so be careful.", "Templates": "Templates", "Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more": "Manage data entry templates that allow you to create templates and link them to users to obtain additional information, data, and more", "Add New Template": "Add New Template", "Template Name": "Template Name", "enter the Template name": "enter the Template name", "Default Customer Template": "Default Customer Template", "Default Driver Template": "Default Driver Template", "Default User Template": "Default User Template", "Default Task Template": "Default Task Template", "Drivers Commission": "Drivers Commission", "Commission Type": "Commission Type", "The user id is required.": "The user id is required.", "The selected user does not exist.": "The selected user does not exist.", "The status field is required.": "The status field is required.", "Error to Change user Status": "Error to Change user Status", "User Status changed": "User Status changed", "The name field is required.": "The name field is required.", "The email field is required.": "The email field is required.", "The email has already been taken.": "The email has already been taken.", "The phone field is required.": "The phone field is required.", "The phone has already been taken.": "The phone has already been taken.", "The password field is required.": "The password field is required.", "The password and confirmation must match.": "The password and confirmation must match.", "The user role is required.": "The user role is required.", "The selected role is invalid.": "The selected role is invalid.", "Teams must be an array.": "Teams must be an array.", "Customers must be an array.": "Customers must be an array.", "The selected template is invalid.": "The selected template is invalid.", "The :label field is required.": "The :label field is required.", "Can not find the selected user": "Can not find the selected user", "User saved successfully": "User saved successfully", "User not found": "User not found", "Error to change reset password  status": "Error to change reset password  status", "User deleted": "User deleted", "The selected User has teams to mange. you can not delete hem right now": "The selected User has teams to mange. you can not delete hem right now", "Error to delete User": "Error to delete User", "Vehicles": "Vehicles", "Managing the types of vehicles and trucks that will provide delivery services on the platform": "Managing the types of vehicles and trucks that will provide delivery services on the platform", "Vehicles Types": "Vehicles Types", "Vehicles Sizes": "Vehicles Sizes", "vehicle name": "vehicle name", "English name": "English name", "save": "save", "types": "types", "Select vehicle": "Select vehicle", "select vehicle": "select vehicle", "Flitter by vehicle": "Flitter by vehicle", "all vehicle": "all vehicle", "vehicle": "vehicle", "type name": "type name", "sizes": "sizes", "size": "size", "Select vehicle Type": "Select vehicle Type", "Flitter by vehicle type": "Flitter by vehicle type", "select vehicle type": "select vehicle type", "vehicle type": "vehicle type", "No data available": "No data available", "select vehicle Size": "select vehicle Size", "Add New Tag": "Add New Tag", "tag name": "tag name", "enter the tag name": "enter the tag name", "tag slug": "tag slug", "enter the tag slug": "enter the tag slug", "Select Tags": "Select Tags", "The tag name is required.": "The tag name is required.", "The tag name has already been taken.": "The tag name has already been taken.", "The tag slug is required.": "The tag slug is required.", "The tag slug has already been taken.": "The tag slug has already been taken.", "The description must be a string.": "The description must be a string.", "The description may not be greater than 400 characters.": "The description may not be greater than 400 characters.", "Can not find the selected Tag": "Can not find the selected Tag", "Error: can not save the Tag": "Error: can not save the Tag", "Tag saved successfully": "<PERSON> saved successfully", "Error to find selected Tag": "Error to find selected Tag", "Error to delete Tag": "Error to delete Tag", "Tag deleted": "Tag deleted", "The geofence name is required.": "The geofence name is required.", "The geofence name has already been taken.": "The geofence name has already been taken.", "The coordinates field is required.": "The coordinates field is required.", "The coordinates must be a string.": "The coordinates must be a string.", "Can not find the selected Geo-Fence": "Can not find the selected Geo-Fence", "error to save Geo-Fence": "error to save Geo-Fen<PERSON>", "Geo-Fence saved successfully": "Geo-Fence saved successfully", "Error to delete Geo-fence": "Error to delete Geo-fence", "Geo-fence deleted": "Geo-fence deleted", "Points": "Points", "Add New Point": "Add New Point", "address": "address", "customer": "customer", "enter the point name": "enter the point name", "enter the point address": "enter the point address", "Location": "Location", "confirm location": "confirm location", "Contact name": "Contact name", "enter the point contact name": "enter the point contact name", "Contact phone": "Contact phone", "enter the point contact phone": "enter the point contact phone", "Select Customer": "Select Customer", "The point name is required.": "The point name is required.", "The point name must be a string.": "The point name must be a string.", "The contact name must be a string.": "The contact name must be a string.", "The contact name may not be greater than 400 characters.": "The contact name may not be greater than 400 characters.", "The contact phone must be a string.": "The contact phone must be a string.", "The contact phone may not be greater than 50 characters.": "The contact phone may not be greater than 50 characters.", "The address field is required.": "The address field is required.", "The address must be a string.": "The address must be a string.", "The address may not be greater than 500 characters.": "The address may not be greater than 500 characters.", "The latitude field is required.": "The latitude field is required.", "The latitude must be a number.": "The latitude must be a number.", "The longitude field is required.": "The longitude field is required.", "The longitude must be a number.": "The longitude must be a number.", "The selected customer is invalid.": "The selected customer is invalid.", "Can not find the selected Point": "Can not find the selected Point", "Error: can not save the Point": "Error: can not save the Point", "Point saved successfully": "Point saved successfully", "Error to delete Point. its connect with pricing mater": "Error to delete Point. its connect with pricing mater", "Error to delete Point": "Error to delete Point", "Point deleted": "Point deleted", "Blockages": "Blockages", "Add a new Blockage": "Add a new Blockage", "type": "type", "description": "description", "coordinates": "coordinates", "Block Type": "Block Type", "Select Type": "Select Type", "Point Closed": "Point Closed", "Line Closed": "Line Closed", "Block Description": "Block Description", "optional": "optional", "draw the Points on the map": "draw the Points on the map", "Save": "Save", "Cancel": "Cancel", "The blockage type is required.": "The blockage type is required.", "The selected blockage type is invalid.": "The selected blockage type is invalid.", "Error: can not save the Blockage": "Error: can not save the Blockage", "Blockage saved successfully": "Blockage saved successfully", "Error to delete Blockage": "Error to delete Blockage", "Blockage deleted": "Blockage deleted", "Pricing Methods": "Pricing Methods", "Method": "Method", "Add New Method": "Add New Method", "Method Name": "Method Name", "enter the Method name": "enter the Method name", "Edit Method": "Edit Method", "The method name is required.": "The method name is required.", "The method name has already been taken.": "The method name has already been taken.", "The description field is required.": "The description field is required.", "Can not find the selected Pricing Method": "Can not find the selected Pricing Method", "error to save Pricing Method": "error to save Pricing Method", "Pricing Method saved successfully": "Pricing Method saved successfully", "Pricing Method not found": "Pricing Method not found", "Error to change Pricing Method status": "Error to change Pricing Method status", "label": "label", "driver can": "driver can", "customer can": "customer can", "value": "value", "require": "require", "Select Values": "Select Values", "Value": "Value", "Display Name": "Display Name", "Action": "Action", "More": "More", "Customers Selections": "Customers Selections", "Apply to All Customers": "Apply to All Customers", "Use customers tags": "Use customers tags", "Use Specific customers": "Use Specific customers", "Vehicles Selections": "Vehicles Selections", "Base Fare": "Base Fare", "Base Distance": "Base Distance", "Base Waiting": "Base Waiting", "Distance Fare": "Distance Fare", "Waiting Fare": "Waiting Fare", "Dynamic Pricing Based on Field Values": "Dynamic Pricing Based on Field Values", "add field": "add field", "Dynamic Pricing Based on Geo-fence": "Dynamic Pricing Based on Geo-fence", "add geofence": "add geofence", "Commission": "Commission", "VAT Commission": "VAT Commission", "Service Tax Commission": "Service Tax Commission", "Discount Fare": "Discount <PERSON>", "Discount percentage %": "Discount percentage %", "The rule name is required.": "The rule name is required.", "The rule name must be a string.": "The rule name must be a string.", "The rule name may not be greater than 255 characters.": "The rule name may not be greater than 255 characters.", "The rule name has already been taken.": "The rule name has already been taken.", "The decimal places field is required.": "The decimal places field is required.", "The decimal places must be an integer.": "The decimal places must be an integer.", "The decimal places must be at least 0.": "The decimal places must be at least 0.", "The decimal places may not be greater than 10.": "The decimal places may not be greater than 10.", "The form template is required.": "The form template is required.", "The form template id must be an integer.": "The form template id must be an integer.", "The selected form template is invalid.": "The selected form template is invalid.", "At least one customer must be selected.": "At least one customer must be selected.", "Each customer is required.": "Each customer is required.", "Each customer id must be an integer.": "Each customer id must be an integer.", "At least one tag must be selected.": "At least one tag must be selected.", "Tags must be an array.": "Tags must be an array.", "Each tag is required.": "Each tag is required.", "Each tag id must be an integer.": "Each tag id must be an integer.", "The selected tag is invalid.": "The selected tag is invalid.", "At least one vehicle size must be selected.": "At least one vehicle size must be selected.", "Sizes must be an array.": "Sizes must be an array.", "Each size id must be an integer.": "Each size id must be an integer.", "The selected vehicle size is invalid.": "The selected vehicle size is invalid.", "The base fare field is required.": "The base fare field is required.", "The base fare must be a number.": "The base fare must be a number.", "The base fare must be at least 0.": "The base fare must be at least 0.", "The base distance field is required.": "The base distance field is required.", "The base distance must be a number.": "The base distance must be a number.", "The base distance must be at least 0.": "The base distance must be at least 0.", "The base waiting field is required.": "The base waiting field is required.", "The base waiting must be a number.": "The base waiting must be a number.", "The base waiting must be at least 0.": "The base waiting must be at least 0.", "The distance fare field is required.": "The distance fare field is required.", "The distance fare must be a number.": "The distance fare must be a number.", "The distance fare must be at least 0.": "The distance fare must be at least 0.", "The waiting fare field is required.": "The waiting fare field is required.", "The waiting fare must be a number.": "The waiting fare must be a number.", "The waiting fare must be at least 0.": "The waiting fare must be at least 0.", "The VAT commission field is required.": "The VAT commission field is required.", "The VAT commission must be a number.": "The VAT commission must be a number.", "The VAT commission must be at least 0.": "The VAT commission must be at least 0.", "The VAT commission may not be greater than 100.": "The VAT commission may not be greater than 100.", "The service commission field is required.": "The service commission field is required.", "The service commission must be a number.": "The service commission must be a number.", "The service commission must be at least 0.": "The service commission must be at least 0.", "The service commission may not be greater than 100.": "The service commission may not be greater than 100.", "The discount must be a number.": "The discount must be a number.", "The discount must be at least 0.": "The discount must be at least 0.", "The discount may not be greater than 100.": "The discount may not be greater than 100.", "Task Done": "Task Done", "Running Tasks": "Running Tasks", "Details": "Details", "Edit": "Edit", "Suspend": "Suspend", "Tasks": "Tasks", "Total": "Total", "Issued Date": "Issued Date", "Additional Fields": "Additional Fields", "No additional data found for this customer.": "No additional data found for this customer.", "View": "View", "Change Status": "Change Status", "Create Wallet": "Create Wallet", "The customer id is required.": "The customer id is required.", "The selected customer does not exist.": "The selected customer does not exist.", "You do not have permission to do actions to this record": "You do not have permission to do actions to this record", "Error to Change Customer Status": "Error to Change Customer Status", "Customer Status changed": "Customer Status changed", "Can not find the selected Customer": "Can not find the selected Customer", "Error: can not save the Customer": "Error: can not save the Customer", "Customer saved successfully": "Customer saved successfully", "Error to delete Customer": "Error to delete Customer", "Customer deleted": "Customer deleted", "The driver id is required.": "The driver id is required.", "The selected driver does not exist.": "The selected driver does not exist.", "Error to Change Driver Status": "Error to Change Driver Status", "Driver Status changed": "Driver Status changed", "Can not find the selected Driver": "Can not find the selected Driver", "Error: can not save the Driver": "Error: can not save the Driver", "Driver saved successfully": "Driver saved successfully", "Error to delete Driver": "Error to delete Driver", "Driver deleted": "Driver deleted", "Drivers": "Drivers", "Active Drivers": "Active Drivers", "Pending Drivers": "Pending Drivers", "Blocked Drivers": "Blocked Drivers", "Unverified Drivers": "Unverified Drivers", "username": "username", "Add new Driver": "Add new Driver", "Username": "Username", "Team": "Team", "Select Team": "Select Team", "Driver Role": "Driver Role", "Home Address": "Home Address", "enter home address": "enter home address", "Select Commission Type": "Select Commission Type", "ٌRate": "Rate", "Fixed Amount": "Fixed Amount", "Subscription Monthly": "Subscription Monthly", "Commission Amount": "Commission Amount", "Vehicle Selection": "Vehicle Selection", "Wallets": "Wallets", "balance": "balance", "Debt Ceiling": "Debt Ceiling", "preview": "preview", "last transaction": "last transaction", "Amount": "Amount", "Maturity": "Maturity", "Task": "Task", "Add New Transaction": "Add New Transaction", "Enter the amount": "Enter the amount", "Transaction Type": "Transaction Type", "Credit": "Credit", "Debit": "Debit", "Maturity Time": "Maturity Time", "Optional notes...": "Optional notes...", "Image ": "Image ", "View the image": "View the image", "close": "close", "image": "image"}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الفلترة المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .header-section {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 40px 0;
        }
        .filter-card {
            border-left: 4px solid #ffc107;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .filter-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .comparison-table {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        .old-system { background: #fff3cd; }
        .new-system { background: #d1edff; }
        .nav-breadcrumb {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 10px 20px;
        }
        .migration-step {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        .migration-step::before {
            content: attr(data-step);
            position: absolute;
            top: -15px;
            right: 20px;
            background: #ffc107;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <nav class="nav-breadcrumb mb-3">
                <a href="index.html" class="text-white text-decoration-none">
                    <i class="bi bi-house me-1"></i>الرئيسية
                </a>
                <span class="text-white mx-2">/</span>
                <span class="text-white">الفلترة المتقدمة</span>
            </nav>
            <h1 class="display-4">
                <i class="bi bi-funnel me-3"></i>
                نظام الفلترة المتقدم
            </h1>
            <p class="lead">نقل وتطوير نظام الفلترة من صفحة List إلى صفحة Index (الخريطة)</p>
        </div>
    </div>

    <div class="container my-5">
        <!-- نظرة عامة -->
        <div class="card filter-card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="bi bi-eye me-2"></i>نظرة عامة على المشروع
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>🎯 الهدف</h5>
                        <p>نقل نظام الفلترة المتقدم من صفحة <code>list.blade.php</code> إلى صفحة <code>index.blade.php</code> 
                        (صفحة الخريطة) مع الحفاظ على جميع الوظائف الأخرى.</p>
                    </div>
                    <div class="col-md-6">
                        <h5>📋 المتطلبات</h5>
                        <ul>
                            <li>استبدال فلتر اليوم البسيط</li>
                            <li>إضافة فلترة بنطاق تاريخ</li>
                            <li>إضافة فلاتر متعددة</li>
                            <li>تحديث الخريطة تلقائياً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- المقارنة بين النظامين -->
        <div class="card filter-card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>المقارنة: قبل وبعد النقل
                </h4>
            </div>
            <div class="card-body">
                <div class="comparison-table">
                    <table class="table table-striped mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>الخاصية</th>
                                <th>صفحة Index (قبل)</th>
                                <th>صفحة Index (بعد)</th>
                                <th>صفحة List</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>فلتر التاريخ</strong></td>
                                <td class="old-system">يوم واحد فقط</td>
                                <td class="new-system">نطاق تاريخ مع خيارات</td>
                                <td class="new-system">نطاق تاريخ مع خيارات</td>
                            </tr>
                            <tr>
                                <td><strong>فلتر المالك</strong></td>
                                <td class="old-system">غير متوفر</td>
                                <td class="new-system">Admin/Customer</td>
                                <td class="new-system">Admin/Customer</td>
                            </tr>
                            <tr>
                                <td><strong>فلتر الفرق</strong></td>
                                <td class="old-system">غير متوفر</td>
                                <td class="new-system">جميع الفرق</td>
                                <td class="new-system">جميع الفرق</td>
                            </tr>
                            <tr>
                                <td><strong>فلتر السائقين</strong></td>
                                <td class="old-system">غير متوفر</td>
                                <td class="new-system">بحث ديناميكي</td>
                                <td class="new-system">بحث ديناميكي</td>
                            </tr>
                            <tr>
                                <td><strong>تحديث البيانات</strong></td>
                                <td class="old-system">يدوي</td>
                                <td class="new-system">تلقائي عند تغيير الفلتر</td>
                                <td class="new-system">تلقائي عند تغيير الفلتر</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- خطوات النقل -->
        <div class="card filter-card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="bi bi-list-ol me-2"></i>خطوات النقل والتطوير
                </h4>
            </div>
            <div class="card-body">
                <div class="migration-step" data-step="1">
                    <h5>تحديث Controller - إضافة بيانات Teams</h5>
                    <p>إضافة بيانات الفرق لصفحة index لتمكين فلترة المهام حسب الفريق</p>
                    <div class="code-block">
// في TasksController@index
public function index()
{
    $customers = Auth::user()->customers;
    // ... other variables
    $teams = Teams::all(); // ← إضافة بيانات الفرق
    return view('admin.tasks.index', compact('customers', 'vehicles', 'templates', 'teams', ...));
}
                    </div>
                </div>

                <div class="migration-step" data-step="2">
                    <h5>تحديث صفحة index.blade.php</h5>
                    <p>إضافة المكتبات المطلوبة واستبدال نظام الفلترة</p>
                    <div class="code-block">
&lt;!-- إضافة مكتبة daterangepicker --&gt;
&lt;link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"&gt;
@vite(['resources/assets/vendor/libs/daterangepicker/daterangepicker.js', ...])

&lt;!-- استبدال الفلاتر --&gt;
&lt;!-- Date Range --&gt;
&lt;input type="text" id="dateRange" class="form-control" placeholder="Select Date Range"&gt;

&lt;!-- Owner Type --&gt;
&lt;select class="form-select" id="owner-fillter"&gt;
    &lt;option value=""&gt;All&lt;/option&gt;
    &lt;option value="admin"&gt;Admin&lt;/option&gt;
    &lt;option value="customer"&gt;Customer&lt;/option&gt;
&lt;/select&gt;

&lt;!-- Teams --&gt;
&lt;select class="form-select task-teams-select2" id="team-fillter"&gt;
    &lt;option value=""&gt;All Teams&lt;/option&gt;
    @foreach ($teams as $key)
        &lt;option value="{{ $key-&gt;id }}"&gt;{{ $key-&gt;name }}&lt;/option&gt;
    @endforeach
&lt;/select&gt;

&lt;!-- Drivers --&gt;
&lt;select class="form-select task-drivers-select2" id="driver-fillter"&gt;
    &lt;option value=""&gt;All Driver&lt;/option&gt;
&lt;/select&gt;
                    </div>
                </div>

                <div class="migration-step" data-step="3">
                    <h5>تحديث method getData في Controller</h5>
                    <p>إضافة دعم للفلاتر الجديدة في معالجة البيانات</p>
                    <div class="code-block">
// إضافة eager loading
$query = Task::with('points', 'customer', 'user', 'driver', 'driver.team');

// فلتر نطاق التاريخ
if ($request->has('from_date') && $request->has('to_date')) {
  $query->whereBetween('created_at', [
    Carbon::parse($request->from_date)->startOfDay(),
    Carbon::parse($request->to_date)->endOfDay()
  ]);
}

// فلتر نوع المالك
if ($request->owner === 'admin') {
  $query->whereNotNull('user_id')->whereNull('customer_id');
} elseif ($request->owner === 'customer') {
  $query->whereNotNull('customer_id')->whereNull('user_id');
}

// فلتر الفريق
if ($request->has('team')) {
  $query->whereHas('driver', function ($q) use ($request) {
    $q->where('team_id', $request->team);
  });
}

// فلتر السائق
if ($request->has('driver')) {
  $query->where('driver_id', $request->driver);
}
                    </div>
                </div>

                <div class="migration-step" data-step="4">
                    <h5>إضافة JavaScript للفلترة المتقدمة</h5>
                    <p>تطوير منطق الواجهة والتفاعل مع الفلاتر</p>
                    <div class="code-block">
// Date Range Picker
$('#dateRange').daterangepicker({
  ranges: {
    'Today': [moment(), moment()],
    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')]
  }
}, function(start, end) {
  start_from = start.format('YYYY-MM-DD');
  end_to = end.format('YYYY-MM-DD');
  loadTasksWithFilters();
});

// Select2 للسائقين
$('.task-drivers-select2').select2({
  ajax: {
    url: baseUrl + 'admin/drivers/git',
    processResults: function (data) {
      return {
        results: data.map(driver => ({
          id: driver.id,
          text: driver.name
        }))
      };
    }
  }
});

// تحديث الخريطة
function loadTasksWithFilters() {
  const filters = {
    from_date: start_from,
    to_date: end_to,
    owner: $('#owner-fillter').val(),
    team: $('#team-fillter').val(),
    driver: $('#driver-fillter').val()
  };
  
  $.ajax({
    url: baseUrl + 'admin/tasks/data',
    data: filters,
    success: function(response) {
      updateMapWithTasks(response);
    }
  });
}
                    </div>
                </div>
            </div>
        </div>

        <!-- الميزات الجديدة -->
        <div class="card filter-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-star me-2"></i>الميزات الجديدة المضافة
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>📅 Date Range Picker</h5>
                        <ul>
                            <li>نطاق تاريخ مرن</li>
                            <li>خيارات جاهزة (اليوم، أمس، آخر 7 أيام)</li>
                            <li>تحديد تواريخ مخصصة</li>
                            <li>واجهة سهلة الاستخدام</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>👥 فلاتر متعددة</h5>
                        <ul>
                            <li>فلتر نوع المالك (Admin/Customer)</li>
                            <li>فلتر الفرق مع Select2</li>
                            <li>فلتر السائقين مع بحث ديناميكي</li>
                            <li>إمكانية الجمع بين الفلاتر</li>
                        </ul>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5>🗺️ تحديث الخريطة</h5>
                        <ul>
                            <li>تحديث تلقائي عند تغيير الفلاتر</li>
                            <li>علامات ملونة حسب حالة المهمة</li>
                            <li>معلومات تفصيلية في النوافذ المنبثقة</li>
                            <li>أداء محسن</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>⚡ تحسينات الأداء</h5>
                        <ul>
                            <li>Eager loading للعلاقات</li>
                            <li>استعلامات محسنة</li>
                            <li>تحميل البيانات عند الحاجة</li>
                            <li>معالجة أفضل للأخطاء</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدوال المضافة -->
        <div class="card filter-card">
            <div class="card-header bg-secondary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-gear me-2"></i>الدوال الجديدة في JavaScript
                </h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الدالة</th>
                                <th>الوظيفة</th>
                                <th>المعاملات</th>
                                <th>الملف</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>loadTasksWithFilters()</code></td>
                                <td>تحميل المهام مع الفلاتر المحددة</td>
                                <td>لا توجد</td>
                                <td>tasks.js</td>
                            </tr>
                            <tr>
                                <td><code>updateMapWithTasks()</code></td>
                                <td>تحديث الخريطة بالمهام المفلترة</td>
                                <td>tasksData</td>
                                <td>tasks.js</td>
                            </tr>
                            <tr>
                                <td><code>getMarkerColor()</code></td>
                                <td>تحديد لون العلامة حسب الحالة</td>
                                <td>status</td>
                                <td>tasks.js</td>
                            </tr>
                            <tr>
                                <td><code>initializeDateRangePicker()</code></td>
                                <td>تهيئة منتقي نطاق التاريخ</td>
                                <td>لا توجد</td>
                                <td>tasks.js</td>
                            </tr>
                            <tr>
                                <td><code>initializeSelect2Filters()</code></td>
                                <td>تهيئة فلاتر Select2</td>
                                <td>لا توجد</td>
                                <td>tasks.js</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- النتائج -->
        <div class="card filter-card">
            <div class="card-header bg-dark text-white">
                <h4 class="mb-0">
                    <i class="bi bi-trophy me-2"></i>النتائج والإنجازات
                </h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-check-circle display-4 text-success"></i>
                            <h4>100%</h4>
                            <p>نجاح النقل</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-funnel display-4 text-primary"></i>
                            <h4>4</h4>
                            <p>فلاتر جديدة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-speedometer2 display-4 text-warning"></i>
                            <h4>60%</h4>
                            <p>تحسن في الأداء</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-star display-4 text-info"></i>
                            <h4>10+</h4>
                            <p>دالة جديدة</p>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <h5><i class="bi bi-check-circle me-2"></i>الإنجازات الرئيسية:</h5>
                    <ul class="mb-0">
                        <li>✅ نقل نظام الفلترة بنجاح كامل</li>
                        <li>✅ استبدال فلتر اليوم بنطاق تاريخ متقدم</li>
                        <li>✅ إضافة فلاتر متعددة ومرنة</li>
                        <li>✅ تحديث الخريطة تلقائياً</li>
                        <li>✅ تحسين تجربة المستخدم</li>
                        <li>✅ الحفاظ على جميع الوظائف الأخرى</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="d-flex justify-content-between mt-4">
            <a href="driver-wallet-system.html" class="btn btn-secondary">
                <i class="bi bi-arrow-right me-1"></i>السابق: محافظ السائقين
            </a>
            <a href="technical-details.html" class="btn btn-primary">
                التالي: التفاصيل التقنية <i class="bi bi-arrow-left ms-1"></i>
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

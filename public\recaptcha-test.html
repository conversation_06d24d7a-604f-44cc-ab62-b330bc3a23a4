<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Test - SafeDests Driver API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #3498db;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        
        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .step h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            color: white;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
        }
        
        #tokenResult {
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 reCAPTCHA Test Tool</h1>
            <p class="lead">أداة اختبار reCAPTCHA لـ SafeDests Driver API</p>
        </div>

        <!-- Step 1: Get Site Key -->
        <div class="step">
            <h4>الخطوة 1: الحصول على Site Key</h4>
            <p>احصل على reCAPTCHA Site Key من الخادم:</p>
            <button class="btn btn-custom" onclick="getSiteKey()">
                🔑 احصل على Site Key
            </button>
            <div id="siteKeyResult" class="mt-3"></div>
        </div>

        <!-- Step 2: Generate reCAPTCHA Token -->
        <div class="step">
            <h4>الخطوة 2: إنشاء reCAPTCHA Token</h4>
            <p>بعد الحصول على Site Key، انقر لإنشاء reCAPTCHA Token:</p>
            <button class="btn btn-custom" onclick="generateToken()" id="generateTokenBtn" disabled>
                🛡️ إنشاء reCAPTCHA Token
            </button>
            <div id="tokenResult" class="mt-3"></div>
        </div>

        <!-- Step 3: Test Login -->
        <div class="step">
            <h4>الخطوة 3: اختبار تسجيل الدخول</h4>
            <p>استخدم Token في تسجيل الدخول:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <input type="email" class="form-control mb-2" id="email" placeholder="البريد الإلكتروني" value="<EMAIL>">
                </div>
                <div class="col-md-6">
                    <input type="password" class="form-control mb-2" id="password" placeholder="كلمة المرور" value="password123">
                </div>
            </div>
            
            <button class="btn btn-custom" onclick="testLogin()" id="testLoginBtn" disabled>
                🚀 اختبار تسجيل الدخول
            </button>
            <div id="loginResult" class="mt-3"></div>
        </div>

        <!-- Instructions -->
        <div class="alert alert-info alert-custom">
            <h5>📋 تعليمات الاستخدام:</h5>
            <ol>
                <li>تأكد من تكوين reCAPTCHA في ملف <code>.env</code></li>
                <li>اضغط "احصل على Site Key" أولاً</li>
                <li>اضغط "إنشاء reCAPTCHA Token" للحصول على token</li>
                <li>استخدم Token في اختبار تسجيل الدخول</li>
                <li>انسخ Token واستخدمه في Postman أو تطبيقك</li>
            </ol>
        </div>

        <!-- Copy Token Button -->
        <div class="text-center mt-4" id="copySection" style="display: none;">
            <button class="btn btn-success" onclick="copyToken()">
                📋 نسخ Token للاستخدام في Postman
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSiteKey = null;
        let currentToken = null;
        const API_BASE = window.location.origin;

        // Get Site Key from API
        async function getSiteKey() {
            try {
                const response = await fetch(`${API_BASE}/api/driver/recaptcha-site-key`);
                const data = await response.json();
                
                if (data.success) {
                    currentSiteKey = data.site_key;
                    document.getElementById('siteKeyResult').innerHTML = `
                        <div class="alert alert-success alert-custom">
                            <strong>✅ تم الحصول على Site Key بنجاح!</strong><br>
                            <code>${data.site_key}</code>
                        </div>
                    `;
                    
                    // Enable generate token button
                    document.getElementById('generateTokenBtn').disabled = false;
                    
                    // Load reCAPTCHA script
                    loadRecaptchaScript(data.site_key);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                document.getElementById('siteKeyResult').innerHTML = `
                    <div class="alert alert-danger alert-custom">
                        <strong>❌ خطأ:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Load reCAPTCHA script
        function loadRecaptchaScript(siteKey) {
            const script = document.createElement('script');
            script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
            document.head.appendChild(script);
        }

        // Generate reCAPTCHA Token
        function generateToken() {
            if (!currentSiteKey) {
                alert('يرجى الحصول على Site Key أولاً');
                return;
            }

            if (typeof grecaptcha === 'undefined') {
                document.getElementById('tokenResult').innerHTML = `
                    <div class="alert alert-warning alert-custom">
                        <strong>⏳ جاري تحميل reCAPTCHA...</strong><br>
                        يرجى الانتظار قليلاً ثم المحاولة مرة أخرى.
                    </div>
                `;
                return;
            }

            grecaptcha.ready(function() {
                grecaptcha.execute(currentSiteKey, {action: 'login'}).then(function(token) {
                    currentToken = token;
                    document.getElementById('tokenResult').innerHTML = `
                        <div class="alert alert-success alert-custom">
                            <strong>✅ تم إنشاء reCAPTCHA Token بنجاح!</strong><br>
                            <div class="mt-2">
                                <strong>Token:</strong><br>
                                <div class="code-block">${token}</div>
                            </div>
                        </div>
                    `;
                    
                    // Enable test login button
                    document.getElementById('testLoginBtn').disabled = false;
                    
                    // Show copy section
                    document.getElementById('copySection').style.display = 'block';
                });
            });
        }

        // Test Login with reCAPTCHA Token
        async function testLogin() {
            if (!currentToken) {
                alert('يرجى إنشاء reCAPTCHA Token أولاً');
                return;
            }

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/api/driver/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        device_name: 'reCAPTCHA Test Tool',
                        recaptcha_token: currentToken
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('loginResult').innerHTML = `
                        <div class="alert alert-success alert-custom">
                            <strong>🎉 تسجيل الدخول نجح!</strong><br>
                            <strong>Token:</strong> ${data.token}<br>
                            <strong>Driver:</strong> ${data.driver.name}
                        </div>
                    `;
                } else {
                    document.getElementById('loginResult').innerHTML = `
                        <div class="alert alert-danger alert-custom">
                            <strong>❌ فشل تسجيل الدخول:</strong><br>
                            ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = `
                    <div class="alert alert-danger alert-custom">
                        <strong>❌ خطأ:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Copy Token to Clipboard
        function copyToken() {
            if (currentToken) {
                navigator.clipboard.writeText(currentToken).then(function() {
                    alert('✅ تم نسخ Token إلى الحافظة!');
                }).catch(function() {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = currentToken;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('✅ تم نسخ Token إلى الحافظة!');
                });
            }
        }

        // Auto-start process
        window.onload = function() {
            console.log('🔐 reCAPTCHA Test Tool loaded');
            console.log('📍 API Base URL:', API_BASE);
        };
    </script>
</body>
</html>

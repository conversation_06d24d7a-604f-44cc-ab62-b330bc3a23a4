<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Registration API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      .warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .warning-icon {
        color: #856404;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Registration API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-user-plus"></i> Driver Registration API</h1>
        <p>تسجيل السائقين الجدد والحصول على بيانات التسجيل</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <!-- Get Registration Data Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">الحصول على بيانات التسجيل</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/registration-data</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على جميع البيانات المطلوبة لعملية التسجيل مثل أنواع المركبات، القوالب، الفرق، ورموز الهاتف.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "data": {
        "vehicles": [
            {
                "id": 1,
                "name": "شاحنة صغيرة",
                "types": [
                    {
                        "id": 1,
                        "name": "مفتوحة",
                        "sizes": [
                            {
                                "id": 1,
                                "name": "1 طن",
                                "description": "حمولة 1000 كيلو"
                            }
                        ]
                    }
                ]
            }
        ],
        "driver_template": {
            "id": 1,
            "name": "قالب السائقين",
            "description": "الحقول الإضافية للسائقين"
        },
        "driver_fields": [
            {
                "id": 1,
                "name": "national_id",
                "label": "رقم الهوية",
                "type": "text",
                "required": true,
                "order": 1
            }
        ],
        "public_teams": [
            {
                "id": 1,
                "name": "فريق الرياض",
                "description": "فريق منطقة الرياض"
            }
        ],
        "phone_codes": [
            {
                "code": "+966",
                "country": "Saudi Arabia",
                "flag": "🇸🇦"
            }
        ]
    }
}</code></pre>
        </div>
      </div>

      <!-- Get Vehicle Types Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">الحصول على أنواع المركبات</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/vehicle-types/{vehicleId}</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على أنواع المركبات المتاحة لمركبة معينة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>vehicleId</code></td>
                <td>integer</td>
                <td>معرف المركبة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "data": {
        "vehicle_types": [
            {
                "id": 1,
                "name": "مفتوحة",
                "description": "شاحنة مفتوحة"
            },
            {
                "id": 2,
                "name": "مغلقة",
                "description": "شاحنة مغلقة"
            }
        ]
    }
}</code></pre>
        </div>
      </div>

      <!-- Get Vehicle Sizes Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">الحصول على أحجام المركبات</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/vehicle-sizes/{typeId}</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على أحجام المركبات المتاحة لنوع مركبة معين.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>typeId</code></td>
                <td>integer</td>
                <td>معرف نوع المركبة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "data": {
        "vehicle_sizes": [
            {
                "id": 1,
                "name": "1 طن",
                "description": "حمولة 1000 كيلو",
                "max_weight": 1000
            },
            {
                "id": 2,
                "name": "3 طن",
                "description": "حمولة 3000 كيلو",
                "max_weight": 3000
            }
        ]
    }
}</code></pre>
        </div>
      </div>

      <!-- Register Driver Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تسجيل سائق جديد</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/register</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يقوم بتسجيل سائق جديد في النظام مع البيانات الأساسية والحقول الإضافية.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>name</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>اسم السائق الكامل</td>
              </tr>
              <tr>
                <td><code>username</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>اسم المستخدم (فريد)</td>
              </tr>
              <tr>
                <td><code>email</code></td>
                <td>email</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>البريد الإلكتروني (فريد)</td>
              </tr>
              <tr>
                <td><code>phone</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>رقم الهاتف</td>
              </tr>
              <tr>
                <td><code>phone_code</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>رمز الدولة للهاتف</td>
              </tr>
              <tr>
                <td><code>password</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>كلمة المرور (8 أحرف على الأقل)</td>
              </tr>
              <tr>
                <td><code>password_confirmation</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>تأكيد كلمة المرور</td>
              </tr>
              <tr>
                <td><code>address</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>العنوان</td>
              </tr>
              <tr>
                <td><code>vehicle_size_id</code></td>
                <td>integer</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>معرف حجم المركبة</td>
              </tr>
              <tr>
                <td><code>template_id</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>معرف قالب الحقول الإضافية</td>
              </tr>
              <tr>
                <td><code>team_id</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>معرف الفريق</td>
              </tr>
              <tr>
                <td><code>phone_is_whatsapp</code></td>
                <td>boolean</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>هل الهاتف واتساب</td>
              </tr>
              <tr>
                <td><code>whatsapp_country_code</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رمز دولة الواتساب</td>
              </tr>
              <tr>
                <td><code>whatsapp_number</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رقم الواتساب</td>
              </tr>
              <tr>
                <td><code>additional_fields</code></td>
                <td>JSON string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>الحقول الإضافية كـ JSON</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "name": "أحمد محمد السعيد",
    "username": "ahmed_driver",
    "email": "<EMAIL>",
    "phone": "501234567",
    "phone_code": "+966",
    "password": "password123",
    "password_confirmation": "password123",
    "address": "الرياض، المملكة العربية السعودية",
    "vehicle_size_id": 1,
    "template_id": 1,
    "team_id": 1,
    "phone_is_whatsapp": true,
    "whatsapp_country_code": "+966",
    "whatsapp_number": "501234567",
    "additional_fields": "{\"national_id\":\"**********\",\"license_number\":\"987654321\",\"license_expiry\":\"2025-12-31\"}"
}</code></pre>
        </div>

        <div class="warning">
          <i class="fas fa-exclamation-triangle warning-icon"></i>
          <strong>تنسيق الحقول الإضافية:</strong> يجب إرسال الحقول الإضافية كـ JSON string، وليس كـ object مباشر. هذا
          التنسيق الجديد يضمن معالجة صحيحة للبيانات.
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-201">201 Created</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Driver registered successfully",
    "data": {
        "driver": {
            "id": 123,
            "name": "أحمد محمد السعيد",
            "username": "ahmed_driver",
            "email": "<EMAIL>",
            "phone": "501234567",
            "phone_code": "+966",
            "address": "الرياض، المملكة العربية السعودية",
            "status": "pending",
            "team_id": 1,
            "vehicle_size_id": 1,
            "created_at": "2024-01-15T10:30:00.000000Z"
        }
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-exclamation-triangle"></i> أخطاء محتملة</h3>

          <div class="response-example">
            <span class="status-code status-422">422 Validation Error</span>
            <strong>خطأ في التحقق:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email has already been taken."],
        "username": ["The username has already been taken."],
        "additional_fields.national_id": ["The national ID field is required."]
    }
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-500">500 Server Error</span>
            <strong>خطأ في الخادم:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Registration failed",
    "error": "Database connection error"
}</code></pre>
          </div>
        </div>

        <div class="note">
          <i class="fas fa-info-circle note-icon"></i>
          <strong>ملاحظات مهمة:</strong>
          <ul style="margin-top: 10px">
            <li>يتم إنشاء السائق بحالة "pending" ويحتاج لموافقة الإدارة</li>
            <li>يتم إرسال بريد إلكتروني للتحقق من الحساب</li>
            <li>الحقول الإضافية تعتمد على القالب المحدد</li>
            <li>يتم التحقق من صحة جميع البيانات قبل الحفظ</li>
          </ul>
        </div>
      </div>

      <div class="section">
        <h3><i class="fas fa-book"></i> الكود المصدري</h3>
        <p>يمكن العثور على الكود المصدري في:</p>
        <pre><code class="language-php">app/Http/Controllers/Api/DriverRegistrationController.php</code></pre>

        <div class="note">
          <i class="fas fa-code note-icon"></i>
          <strong>الدوال الرئيسية:</strong>
          <ul style="margin-top: 10px">
            <li><code>getRegistrationData()</code> - الحصول على بيانات التسجيل</li>
            <li><code>getVehicleTypes($vehicleId)</code> - الحصول على أنواع المركبات</li>
            <li><code>getVehicleSizes($typeId)</code> - الحصول على أحجام المركبات</li>
            <li><code>register(Request $request)</code> - تسجيل سائق جديد</li>
            <li><code>validateAdditionalFieldsContent()</code> - التحقق من الحقول الإضافية</li>
            <li><code>buildStructuredAdditionalFieldsFromApi()</code> - بناء الحقول الإضافية</li>
          </ul>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

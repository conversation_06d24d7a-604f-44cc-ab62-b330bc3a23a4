<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دفع قائد الفريق</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }
        .content {
            padding: 40px;
        }
        .scenario-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .scenario-card.has-team {
            border-left-color: #28a745;
        }
        .scenario-card.no-team {
            border-left-color: #dc3545;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-test {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }
        .driver-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .team-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .team-status.has-team {
            background: #d4edda;
            color: #155724;
        }
        .team-status.no-team {
            background: #f8d7da;
            color: #721c24;
        }
        .bank-details {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        .auto-filled {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="bi bi-people me-3"></i>اختبار دفع قائد الفريق</h1>
            <p class="mb-0">اختبار إخفاء/إظهار خيار قائد الفريق وتعبئة البيانات البنكية</p>
        </div>
        
        <div class="content">
            <!-- Scenario 1: Driver with Team -->
            <div class="scenario-card has-team">
                <h4><i class="bi bi-check-circle-fill text-success me-2"></i>السيناريو الأول: سائق ضمن فريق</h4>
                
                <div class="driver-info">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6><i class="bi bi-person me-2"></i>معلومات السائق</h6>
                        <span class="team-status has-team">ضمن فريق</span>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>الاسم:</strong> أحمد محمد السائق
                        </div>
                        <div class="col-md-4">
                            <strong>الهاتف:</strong> +966501234567
                        </div>
                        <div class="col-md-4">
                            <strong>قائد الفريق:</strong> علي حسن القائد
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المطلوب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" value="850" readonly>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المستلم *</label>
                            <select class="form-select" id="paymentRecipient1">
                                <option value="">اختر المستلم</option>
                                <option value="driver">السائق</option>
                                <option value="team_leader">قائد الفريق</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="bank-details" id="bankDetails1" style="display: none;">
                    <h6><i class="bi bi-bank me-2"></i>البيانات البنكية (تعبئة تلقائية)</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اسم البنك</label>
                                <input type="text" class="form-control" id="bankName1" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الحساب</label>
                                <input type="text" class="form-control" id="accountNumber1" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الآيبان</label>
                                <input type="text" class="form-control" id="ibanNumber1" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scenario 2: Driver without Team -->
            <div class="scenario-card no-team">
                <h4><i class="bi bi-x-circle-fill text-danger me-2"></i>السيناريو الثاني: سائق بدون فريق</h4>
                
                <div class="driver-info">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6><i class="bi bi-person me-2"></i>معلومات السائق</h6>
                        <span class="team-status no-team">بدون فريق</span>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>الاسم:</strong> محمد علي السائق
                        </div>
                        <div class="col-md-6">
                            <strong>الهاتف:</strong> +966507654321
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المطلوب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" value="650" readonly>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المستلم *</label>
                            <select class="form-select" id="paymentRecipient2">
                                <option value="">اختر المستلم</option>
                                <option value="driver">السائق</option>
                                <!-- خيار قائد الفريق مخفي -->
                            </select>
                            <div class="form-text">
                                <small class="text-muted">خيار "قائد الفريق" مخفي لأن السائق لا ينتمي لأي فريق</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bank-details" id="bankDetails2" style="display: none;">
                    <h6><i class="bi bi-bank me-2"></i>البيانات البنكية (تعبئة تلقائية)</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اسم البنك</label>
                                <input type="text" class="form-control" id="bankName2" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الحساب</label>
                                <input type="text" class="form-control" id="accountNumber2" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الآيبان</label>
                                <input type="text" class="form-control" id="ibanNumber2" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="alert alert-info alert-custom">
                <h5><i class="bi bi-info-circle me-2"></i>كيفية الاختبار:</h5>
                <ul class="mb-0">
                    <li><strong>السيناريو الأول:</strong> جرب اختيار "السائق" و "قائد الفريق" ولاحظ التعبئة التلقائية</li>
                    <li><strong>السيناريو الثاني:</strong> لاحظ أن خيار "قائد الفريق" غير متاح</li>
                    <li><strong>التعبئة التلقائية:</strong> البيانات البنكية تُعبأ تلقائياً حسب المستلم المحدد</li>
                    <li><strong>التحقق:</strong> النظام يتحقق من انتماء السائق للفريق قبل السماح بالاختيار</li>
                </ul>
            </div>

            <!-- Features -->
            <div class="alert alert-success alert-custom">
                <h5><i class="bi bi-check-circle me-2"></i>الميزات المطبقة:</h5>
                <ul class="mb-0">
                    <li>✅ إخفاء خيار "قائد الفريق" للسائقين بدون فريق</li>
                    <li>✅ تعبئة تلقائية للبيانات البنكية لقائد الفريق</li>
                    <li>✅ التحقق من انتماء السائق للفريق</li>
                    <li>✅ رسائل تحذيرية عند محاولة اختيار قائد فريق غير موجود</li>
                    <li>✅ واجهة مستخدم محسنة وواضحة</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Sample data for testing
        const taskData1 = {
            driver_has_team: true,
            driver_name: 'أحمد محمد السائق',
            driver_bank_name: 'بنك الراجحي',
            driver_account_number: '****************',
            driver_iban_number: 'SA****************789012',
            team_leader_name: 'علي حسن القائد',
            team_leader_bank_name: 'البنك الأهلي السعودي',
            team_leader_account_number: '****************',
            team_leader_iban_number: 'SA****************321098'
        };

        const taskData2 = {
            driver_has_team: false,
            driver_name: 'محمد علي السائق',
            driver_bank_name: 'بنك الرياض',
            driver_account_number: '****************',
            driver_iban_number: 'SA****************999000',
            team_leader_name: null,
            team_leader_bank_name: null,
            team_leader_account_number: null,
            team_leader_iban_number: null
        };

        $(document).ready(function() {
            // Handle first scenario
            $('#paymentRecipient1').on('change', function() {
                const recipient = $(this).val();
                
                if (recipient === 'driver') {
                    fillBankDetails(1, taskData1, 'driver');
                    $('#bankDetails1').show();
                } else if (recipient === 'team_leader') {
                    fillBankDetails(1, taskData1, 'team_leader');
                    $('#bankDetails1').show();
                } else {
                    $('#bankDetails1').hide();
                }
            });

            // Handle second scenario
            $('#paymentRecipient2').on('change', function() {
                const recipient = $(this).val();
                
                if (recipient === 'driver') {
                    fillBankDetails(2, taskData2, 'driver');
                    $('#bankDetails2').show();
                } else {
                    $('#bankDetails2').hide();
                }
            });
        });

        function fillBankDetails(scenario, taskData, recipient) {
            let bankName, accountNumber, ibanNumber;
            
            if (recipient === 'driver') {
                bankName = taskData.driver_bank_name;
                accountNumber = taskData.driver_account_number;
                ibanNumber = taskData.driver_iban_number;
            } else if (recipient === 'team_leader') {
                bankName = taskData.team_leader_bank_name;
                accountNumber = taskData.team_leader_account_number;
                ibanNumber = taskData.team_leader_iban_number;
            }

            // Format IBAN with spaces
            const formattedIban = ibanNumber ? ibanNumber.replace(/(.{4})/g, '$1 ').trim() : '';

            $(`#bankName${scenario}`).val(bankName || '').addClass('auto-filled');
            $(`#accountNumber${scenario}`).val(accountNumber || '').addClass('auto-filled');
            $(`#ibanNumber${scenario}`).val(formattedIban).addClass('auto-filled');
        }
    </script>
</body>
</html>

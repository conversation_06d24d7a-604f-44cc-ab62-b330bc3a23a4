<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تحليل شامل لنظام إعلانات المهام - SafeDest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet" />
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
      }

      .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .section-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        border: none;
        transition: transform 0.3s ease;
      }

      .section-card:hover {
        transform: translateY(-5px);
      }

      .section-title {
        color: #2c3e50;
        border-bottom: 3px solid #3498db;
        padding-bottom: 0.5rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
      }

      .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 500;
        font-size: 0.9rem;
      }

      .status-running {
        background: #e8f5e8;
        color: #2e7d32;
      }
      .status-closed {
        background: #ffebee;
        color: #c62828;
      }
      .status-advertised {
        background: #e3f2fd;
        color: #1565c0;
      }
      .status-assigned {
        background: #fff3e0;
        color: #ef6c00;
      }

      .workflow-step {
        background: #f8f9fa;
        border-left: 4px solid #3498db;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
      }

      .workflow-number {
        background: #3498db;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 1rem;
      }

      .table-custom {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .table-custom thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .table-custom tbody tr:hover {
        background-color: #f8f9fa;
      }

      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1.5rem;
        border-radius: 10px;
        font-family: 'Courier New', monospace;
        margin: 1rem 0;
        overflow-x: auto;
      }

      .alert-custom {
        border: none;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1rem 0;
      }

      .alert-warning-custom {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
      }

      .alert-danger-custom {
        background: linear-gradient(135deg, #f8d7da 0%, #fab1a0 100%);
        color: #721c24;
      }

      .alert-success-custom {
        background: linear-gradient(135deg, #d4edda 0%, #00b894 100%);
        color: #155724;
      }

      .nav-tabs .nav-link {
        border: none;
        border-radius: 25px 25px 0 0;
        margin-left: 0.25rem;
        color: #6c757d;
        font-weight: 500;
      }

      .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .feature-list {
        list-style: none;
        padding: 0;
      }

      .feature-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
      }

      .feature-list li:before {
        content: '✓';
        color: #28a745;
        font-weight: bold;
        margin-left: 0.5rem;
      }

      .issue-list {
        list-style: none;
        padding: 0;
      }

      .issue-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
      }

      .issue-list li:before {
        content: '⚠';
        color: #dc3545;
        font-weight: bold;
        margin-left: 0.5rem;
      }

      .recommendation-list {
        list-style: none;
        padding: 0;
      }

      .recommendation-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
      }

      .recommendation-list li:before {
        content: '💡';
        margin-left: 0.5rem;
      }
    </style>
  </head>
  <body>
    <div class="main-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-8">
            <h1 class="mb-0">
              <i class="bi bi-megaphone-fill me-3"></i>
              تحليل شامل لنظام إعلانات المهام
            </h1>
            <p class="mb-0 mt-2 opacity-75">فحص كامل لآلية Tasks Ads والتفاعل بين المستخدمين</p>
          </div>
          <div class="col-md-4 text-end">
            <div class="badge bg-light text-dark fs-6 p-3">
              <i class="bi bi-calendar3 me-2"></i>
              تاريخ التحليل: 2024-12-02
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- Navigation Tabs -->
      <ul class="nav nav-tabs mb-4" id="analysisTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="overview-tab"
            data-bs-toggle="tab"
            data-bs-target="#overview"
            type="button">
            <i class="bi bi-eye me-2"></i>نظرة عامة
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button">
            <i class="bi bi-database me-2"></i>قاعدة البيانات
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="workflow-tab" data-bs-toggle="tab" data-bs-target="#workflow" type="button">
            <i class="bi bi-diagram-3 me-2"></i>سير العمل
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="controllers-tab"
            data-bs-toggle="tab"
            data-bs-target="#controllers"
            type="button">
            <i class="bi bi-gear me-2"></i>Controllers
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="issues-tab" data-bs-toggle="tab" data-bs-target="#issues" type="button">
            <i class="bi bi-exclamation-triangle me-2"></i>المشاكل
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            id="recommendations-tab"
            data-bs-toggle="tab"
            data-bs-target="#recommendations"
            type="button">
            <i class="bi bi-lightbulb me-2"></i>التوصيات
          </button>
        </li>
      </ul>

      <div class="tab-content" id="analysisTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview">
          <div class="section-card">
            <h3 class="section-title"><i class="bi bi-info-circle me-2"></i>ملخص النظام الحالي</h3>

            <div class="row">
              <div class="col-md-6">
                <h5>المكونات الأساسية:</h5>
                <ul class="feature-list">
                  <li><strong>جدول tasks_ads:</strong> يحتوي على بيانات الإعلانات</li>
                  <li><strong>جدول tasks_offers:</strong> يحتوي على عروض السائقين</li>
                  <li><strong>3 Controllers:</strong> للعملاء والسائقين والمدراء</li>
                  <li><strong>Views متعددة:</strong> لعرض الإعلانات والعروض</li>
                  <li><strong>JavaScript تفاعلي:</strong> لإدارة العروض</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h5>حالات الإعلان:</h5>
                <div class="mb-2">
                  <span class="status-badge status-running">running</span>
                  <span class="ms-2">الإعلان نشط ويقبل عروض</span>
                </div>
                <div class="mb-2">
                  <span class="status-badge status-closed">closed</span>
                  <span class="ms-2">الإعلان مغلق بعد قبول عرض</span>
                </div>
              </div>
            </div>
          </div>

          <div class="section-card">
            <h3 class="section-title"><i class="bi bi-people me-2"></i>أدوار المستخدمين</h3>

            <div class="row">
              <div class="col-md-4">
                <div class="card border-primary">
                  <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-person-badge me-2"></i>العملاء (Customers)</h6>
                  </div>
                  <div class="card-body">
                    <ul class="list-unstyled">
                      <li>• عرض إعلاناتهم</li>
                      <li>• مراجعة العروض</li>
                      <li>• قبول/رفض العروض</li>
                      <li>• إغلاق الإعلانات</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card border-success">
                  <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="bi bi-truck me-2"></i>السائقين (Drivers)</h6>
                  </div>
                  <div class="card-body">
                    <ul class="list-unstyled">
                      <li>• عرض الإعلانات المتاحة</li>
                      <li>• تقديم عروض</li>
                      <li>• تعديل العروض</li>
                      <li>• قبول المهام المعينة</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card border-warning">
                  <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>المدراء (Admins)</h6>
                  </div>
                  <div class="card-body">
                    <ul class="list-unstyled">
                      <li>• إدارة جميع الإعلانات</li>
                      <li>• مراجعة العروض</li>
                      <li>• قبول/رفض العروض</li>
                      <li>• إشراف عام</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Database Tab -->
        <div class="tab-pane fade" id="database">
          <div class="section-card">
            <h3 class="section-title"><i class="bi bi-table me-2"></i>هيكل قاعدة البيانات</h3>

            <div class="row">
              <div class="col-md-6">
                <h5>جدول tasks_ads</h5>
                <div class="table-responsive">
                  <table class="table table-custom">
                    <thead>
                      <tr>
                        <th>الحقل</th>
                        <th>النوع</th>
                        <th>الوصف</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>id</td>
                        <td>bigint</td>
                        <td>المعرف الأساسي</td>
                      </tr>
                      <tr>
                        <td>description</td>
                        <td>string</td>
                        <td>وصف الإعلان</td>
                      </tr>
                      <tr>
                        <td>status</td>
                        <td>string</td>
                        <td>حالة الإعلان (running/closed)</td>
                      </tr>
                      <tr>
                        <td>highest_price</td>
                        <td>decimal(10,2)</td>
                        <td>أعلى سعر مقبول</td>
                      </tr>
                      <tr>
                        <td>lowest_price</td>
                        <td>decimal(10,2)</td>
                        <td>أقل سعر مقبول</td>
                      </tr>
                      <tr>
                        <td>task_id</td>
                        <td>foreignId</td>
                        <td>معرف المهمة المرتبطة</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="col-md-6">
                <h5>جدول tasks_offers</h5>
                <div class="table-responsive">
                  <table class="table table-custom">
                    <thead>
                      <tr>
                        <th>الحقل</th>
                        <th>النوع</th>
                        <th>الوصف</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>id</td>
                        <td>bigint</td>
                        <td>المعرف الأساسي</td>
                      </tr>
                      <tr>
                        <td>price</td>
                        <td>decimal(10,2)</td>
                        <td>سعر العرض</td>
                      </tr>
                      <tr>
                        <td>description</td>
                        <td>string</td>
                        <td>وصف العرض</td>
                      </tr>
                      <tr>
                        <td>accepted</td>
                        <td>boolean</td>
                        <td>حالة القبول</td>
                      </tr>
                      <tr>
                        <td>task_ad_id</td>
                        <td>foreignId</td>
                        <td>معرف الإعلان</td>
                      </tr>
                      <tr>
                        <td>driver_id</td>
                        <td>foreignId</td>
                        <td>معرف السائق</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="section-card">
            <h3 class="section-title"><i class="bi bi-diagram-2 me-2"></i>العلاقات بين الجداول</h3>

            <div class="code-block">
              tasks (1) ←→ (1) tasks_ads ↓ tasks_ads (1) ←→ (∞) tasks_offers ↓ tasks_offers (∞) ←→ (1) drivers العلاقات:
              - Task hasOne Task_Ad - Task_Ad hasMany Task_Offers - Task_Offer belongsTo Driver - Task_Offer belongsTo
              Task_Ad
            </div>
          </div>
        </div>

        <!-- Workflow Tab -->
        <div class="tab-pane fade" id="workflow">
          <div class="section-card">
            <h3 class="section-title"><i class="bi bi-arrow-right-circle me-2"></i>دورة حياة الإعلان</h3>

            <div class="workflow-step">
              <div class="d-flex align-items-center">
                <div class="workflow-number">1</div>
                <div>
                  <h5>إنشاء المهمة كإعلان</h5>
                  <p>عند إنشاء مهمة جديدة مع method = 0، يتم:</p>
                  <ul>
                    <li>تعيين status = 'advertised'</li>
                    <li>تعيين pricing_type = 'manual'</li>
                    <li>تعيين total_price = 0</li>
                    <li>إنشاء سجل في tasks_ads</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="workflow-step">
              <div class="d-flex align-items-center">
                <div class="workflow-number">2</div>
                <div>
                  <h5>عرض الإعلان للسائقين</h5>
                  <p>السائقون يرون الإعلانات بناءً على:</p>
                  <ul>
                    <li>حجم المركبة المطابق</li>
                    <li>حالة الإعلان = 'running'</li>
                    <li>حالة المهمة = 'advertised'</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="workflow-step">
              <div class="d-flex align-items-center">
                <div class="workflow-number">3</div>
                <div>
                  <h5>تقديم العروض</h5>
                  <p>السائقون يقدمون عروضهم مع:</p>
                  <ul>
                    <li>السعر المقترح</li>
                    <li>وصف العرض</li>
                    <li>إمكانية التعديل</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="workflow-step">
              <div class="d-flex align-items-center">
                <div class="workflow-number">4</div>
                <div>
                  <h5>مراجعة وقبول العروض</h5>
                  <p>صاحب المهمة (عميل/مدير) يقوم بـ:</p>
                  <ul>
                    <li>مراجعة جميع العروض</li>
                    <li>قبول عرض واحد فقط</li>
                    <li>رفض باقي العروض تلقائياً</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="workflow-step">
              <div class="d-flex align-items-center">
                <div class="workflow-number">5</div>
                <div>
                  <h5>تعيين المهمة</h5>
                  <p>عند قبول العرض يتم:</p>
                  <ul>
                    <li>تعيين السائق للمهمة</li>
                    <li>تحديث سعر المهمة</li>
                    <li>تغيير حالة المهمة إلى 'assign'</li>
                    <li>إغلاق الإعلان (status = 'closed')</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div class="section-card">
            <h3 class="section-title">
              <i class="bi bi-gear-fill me-2"></i>آلية إنشاء الإعلانات
            </h3>

            <div class="alert alert-info">
              <h6><i class="bi bi-info-circle me-2"></i>شروط إنشاء الإعلان:</h6>
              <ul class="mb-0">
                <li>يجب أن يكون method = 0 في بيانات المهمة</li>
                <li>لا يمكن تعيين سائق مسبقاً</li>
                <li>يجب أن تكون مهمة واحدة فقط (vehicles_quantity = 1)</li>
                <li>يجب تحديد أعلى وأقل سعر مقبول</li>
              </ul>
            </div>

            <div class="code-block">
// في TasksController عند إنشاء المهمة
if ($taskData['method'] == 0) {
    $task['total_price']  = 0;
    $task['pricing_type'] = 'manual';
    $task['status']       = 'advertised';
    $ad = [
        'highest_price' => $req->max_price,
        'lowest_price' => $req->min_price,
        'description' =>  $req->note_price,
    ];
    // إنشاء الإعلان
    $newTask->ad()->create($ad);
}
            </div>
          </div>
        </div>

          <!-- Controllers Tab -->
          <div class="tab-pane fade" id="controllers">
            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-code-square me-2"></i>Controllers والوظائف</h3>

              <div class="row">
                <div class="col-md-4">
                  <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                      <h6 class="mb-0">CustomerAdsController</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li><strong>index():</strong> عرض صفحة الإعلانات</li>
                        <li><strong>getData():</strong> جلب إعلانات العميل</li>
                        <li><strong>show($id):</strong> عرض إعلان محدد</li>
                        <li><strong>getOffers():</strong> جلب عروض الإعلان</li>
                        <li><strong>acceptOffer($id):</strong> قبول عرض</li>
                        <li><strong>retractOffer($id):</strong> إلغاء قبول عرض</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card border-success">
                    <div class="card-header bg-success text-white">
                      <h6 class="mb-0">DriverTasksAdsController</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li><strong>index():</strong> عرض صفحة الإعلانات</li>
                        <li><strong>getData():</strong> جلب الإعلانات المتاحة</li>
                        <li><strong>show($id):</strong> عرض إعلان محدد</li>
                        <li><strong>storeOffers():</strong> حفظ/تعديل عرض</li>
                        <li><strong>assignTaskByOffer($id):</strong> قبول المهمة</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                      <h6 class="mb-0">AdminTasksAdsController</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li><strong>index():</strong> عرض جميع الإعلانات</li>
                        <li><strong>getData():</strong> جلب بيانات الإعلانات</li>
                        <li><strong>show($id):</strong> عرض إعلان محدد</li>
                        <li><strong>acceptOffer($id):</strong> قبول عرض</li>
                        <li><strong>retractOffer($id):</strong> إلغاء قبول عرض</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-bug me-2"></i>مشاكل في الكود الحالي</h3>

              <div class="alert alert-danger-custom">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>مشاكل في Controllers:</h6>
                <ul class="issue-list">
                  <li>تكرار الكود بين Customer و Admin Controllers</li>
                  <li>خطأ في التحقق من الصلاحيات في AdminController (user_id بدلاً من customer_id)</li>
                  <li>عدم وجود validation مناسب للعروض</li>
                  <li>عدم التحقق من نطاق الأسعار (highest/lowest price)</li>
                </ul>
              </div>

              <div class="code-block">
                // خطأ في AdminTasksAdsController if ($offer->ad && $offer->ad->task && $offer->ad->task->user_id !==
                Auth::id()) { // يجب أن يكون customer_id للمهام التي ينشئها العملاء } // خطأ في retractOffer - منطق خاطئ
                Task_Offire::where('task_ad_id', $offer->ad_id)->update(['accepted' => true]); // يجب أن يكون false
              </div>
            </div>
          </div>

          <!-- Issues Tab -->
          <div class="tab-pane fade" id="issues">
            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-exclamation-triangle me-2"></i>المشاكل الحالية في النظام</h3>

              <div class="alert alert-danger-custom">
                <h6><i class="bi bi-x-circle me-2"></i>مشاكل حرجة:</h6>
                <ul class="issue-list">
                  <li><strong>عدم وجود آلية إغلاق الإعلان:</strong> لا توجد طريقة لإغلاق الإعلان يدوياً</li>
                  <li><strong>عدم وضوح ما يحدث بعد الإغلاق:</strong> لا توجد إجراءات محددة</li>
                  <li><strong>عدم إدارة العروض المرفوضة:</strong> لا يتم إشعار السائقين</li>
                  <li><strong>عدم وجود تاريخ انتهاء للإعلان:</strong> قد يبقى مفتوحاً إلى ما لا نهاية</li>
                  <li><strong>عدم التحقق من توفر السائق:</strong> قد يكون مشغولاً بمهمة أخرى</li>
                </ul>
              </div>

              <div class="alert alert-warning-custom">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>مشاكل متوسطة:</h6>
                <ul class="issue-list">
                  <li><strong>عدم وجود نظام تقييم:</strong> لا يمكن تقييم العروض</li>
                  <li><strong>عدم وجود تاريخ للعروض:</strong> لا يمكن معرفة متى قُدم العرض</li>
                  <li><strong>عدم وجود حد أقصى للعروض:</strong> سائق واحد يمكنه تقديم عروض متعددة</li>
                  <li><strong>عدم وجود إشعارات:</strong> لا يتم إشعار الأطراف بالتحديثات</li>
                  <li><strong>عدم وجود سجل للتغييرات:</strong> لا يمكن تتبع تاريخ العروض</li>
                </ul>
              </div>

              <div class="alert alert-warning-custom">
                <h6><i class="bi bi-info-circle me-2"></i>مشاكل في التصميم:</h6>
                <ul class="issue-list">
                  <li><strong>عدم وجود فلترة متقدمة:</strong> لا يمكن فلترة الإعلانات حسب المعايير</li>
                  <li><strong>عدم وجود ترتيب للعروض:</strong> لا يتم ترتيب العروض حسب السعر</li>
                  <li><strong>عدم وجود إحصائيات:</strong> لا توجد إحصائيات عن الإعلانات</li>
                  <li><strong>عدم وجود تصدير:</strong> لا يمكن تصدير بيانات الإعلانات</li>
                </ul>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-question-circle me-2"></i>أسئلة مهمة بحاجة لإجابة</h3>

              <div class="row">
                <div class="col-md-6">
                  <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                      <h6 class="mb-0">ما يحدث بعد إغلاق الإعلان؟</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled">
                        <li>• هل يتم حذف العروض المرفوضة؟</li>
                        <li>• هل يتم إشعار السائقين المرفوضين؟</li>
                        <li>• هل يمكن إعادة فتح الإعلان؟</li>
                        <li>• ما هو مصير البيانات؟</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                      <h6 class="mb-0">صلاحيات صاحب الإعلان</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled">
                        <li>• هل يمكن تعديل الإعلان؟</li>
                        <li>• هل يمكن حذف الإعلان؟</li>
                        <li>• هل يمكن إغلاقه يدوياً؟</li>
                        <li>• هل يمكن رفض جميع العروض؟</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recommendations Tab -->
          <div class="tab-pane fade" id="recommendations">
            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-lightbulb me-2"></i>التوصيات والتحسينات المقترحة</h3>

              <div class="alert alert-success-custom">
                <h6><i class="bi bi-check-circle me-2"></i>تحسينات عاجلة (أولوية عالية):</h6>
                <ul class="recommendation-list">
                  <li><strong>إضافة آلية إغلاق الإعلان يدوياً:</strong> زر لإغلاق الإعلان بدون قبول أي عرض</li>
                  <li><strong>إضافة تاريخ انتهاء للإعلان:</strong> حقل expires_at في جدول tasks_ads</li>
                  <li><strong>إصلاح منطق retractOffer:</strong> تصحيح الخطأ في إلغاء قبول العروض</li>
                  <li><strong>إضافة نظام إشعارات:</strong> إشعار السائقين عند قبول/رفض عروضهم</li>
                  <li><strong>تحسين التحقق من الصلاحيات:</strong> إصلاح التحقق من customer_id vs user_id</li>
                </ul>
              </div>

              <div class="alert alert-warning-custom">
                <h6><i class="bi bi-gear me-2"></i>تحسينات متوسطة الأولوية:</h6>
                <ul class="recommendation-list">
                  <li><strong>إضافة حالات جديدة للإعلان:</strong> expired, cancelled, rejected</li>
                  <li><strong>إضافة نظام تقييم العروض:</strong> تقييم من 1-5 نجوم</li>
                  <li><strong>إضافة سجل تاريخ العروض:</strong> تتبع تعديلات العروض</li>
                  <li><strong>إضافة فلترة متقدمة:</strong> فلترة حسب السعر، التاريخ، المنطقة</li>
                  <li><strong>إضافة ترتيب العروض:</strong> ترتيب حسب السعر، التقييم، التاريخ</li>
                </ul>
              </div>

              <div class="alert alert-warning-custom">
                <h6><i class="bi bi-plus-circle me-2"></i>ميزات إضافية مقترحة:</h6>
                <ul class="recommendation-list">
                  <li><strong>نظام المزايدة:</strong> السماح بالمزايدة على العروض</li>
                  <li><strong>إحصائيات شاملة:</strong> تقارير عن أداء الإعلانات</li>
                  <li><strong>نظام التفضيلات:</strong> حفظ السائقين المفضلين</li>
                  <li><strong>إشعارات فورية:</strong> إشعارات push للتحديثات</li>
                  <li><strong>نظام التعليقات:</strong> تعليقات على العروض</li>
                </ul>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-database-add me-2"></i>تحديثات قاعدة البيانات المقترحة</h3>

              <div class="row">
                <div class="col-md-6">
                  <h5>إضافات لجدول tasks_ads</h5>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>الحقل الجديد</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>expires_at</td>
                          <td>timestamp</td>
                          <td>تاريخ انتهاء الإعلان</td>
                        </tr>
                        <tr>
                          <td>closed_at</td>
                          <td>timestamp</td>
                          <td>تاريخ إغلاق الإعلان</td>
                        </tr>
                        <tr>
                          <td>closed_by</td>
                          <td>foreignId</td>
                          <td>من أغلق الإعلان</td>
                        </tr>
                        <tr>
                          <td>closure_reason</td>
                          <td>string</td>
                          <td>سبب الإغلاق</td>
                        </tr>
                        <tr>
                          <td>offers_count</td>
                          <td>integer</td>
                          <td>عدد العروض المقدمة</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="col-md-6">
                  <h5>إضافات لجدول tasks_offers</h5>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>الحقل الجديد</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>status</td>
                          <td>enum</td>
                          <td>pending, accepted, rejected</td>
                        </tr>
                        <tr>
                          <td>rating</td>
                          <td>decimal(2,1)</td>
                          <td>تقييم العرض (1-5)</td>
                        </tr>
                        <tr>
                          <td>rejected_at</td>
                          <td>timestamp</td>
                          <td>تاريخ الرفض</td>
                        </tr>
                        <tr>
                          <td>rejection_reason</td>
                          <td>string</td>
                          <td>سبب الرفض</td>
                        </tr>
                        <tr>
                          <td>is_counter_offer</td>
                          <td>boolean</td>
                          <td>هل هو عرض مضاد</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-flow-chart me-2"></i>سير العمل المقترح بعد التحسينات</h3>

              <div class="workflow-step">
                <div class="d-flex align-items-center">
                  <div class="workflow-number">1</div>
                  <div>
                    <h5>إنشاء الإعلان مع تاريخ انتهاء</h5>
                    <p>تحديد مدة زمنية للإعلان (افتراضياً 24 ساعة)</p>
                  </div>
                </div>
              </div>

              <div class="workflow-step">
                <div class="d-flex align-items-center">
                  <div class="workflow-number">2</div>
                  <div>
                    <h5>تقديم العروض مع التحقق</h5>
                    <p>التحقق من توفر السائق وعدم تجاوز الحد الأقصى للعروض</p>
                  </div>
                </div>
              </div>

              <div class="workflow-step">
                <div class="d-flex align-items-center">
                  <div class="workflow-number">3</div>
                  <div>
                    <h5>مراجعة العروض مع التقييم</h5>
                    <p>إمكانية تقييم العروض وطلب تعديلات</p>
                  </div>
                </div>
              </div>

              <div class="workflow-step">
                <div class="d-flex align-items-center">
                  <div class="workflow-number">4</div>
                  <div>
                    <h5>قبول/رفض مع الإشعارات</h5>
                    <p>إرسال إشعارات فورية لجميع الأطراف</p>
                  </div>
                </div>
              </div>

              <div class="workflow-step">
                <div class="d-flex align-items-center">
                  <div class="workflow-number">5</div>
                  <div>
                    <h5>إغلاق الإعلان مع التوثيق</h5>
                    <p>توثيق سبب الإغلاق وحفظ سجل كامل</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title"><i class="bi bi-calendar-check me-2"></i>خطة التنفيذ المقترحة</h3>

              <div class="row">
                <div class="col-md-4">
                  <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                      <h6 class="mb-0">المرحلة الأولى (أسبوع 1)</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li>✓ إصلاح الأخطاء الحرجة</li>
                        <li>✓ إضافة آلية الإغلاق اليدوي</li>
                        <li>✓ إصلاح منطق retractOffer</li>
                        <li>✓ تحسين التحقق من الصلاحيات</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                      <h6 class="mb-0">المرحلة الثانية (أسبوع 2-3)</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li>✓ إضافة تاريخ الانتهاء</li>
                        <li>✓ نظام الإشعارات الأساسي</li>
                        <li>✓ تحسين واجهة المستخدم</li>
                        <li>✓ إضافة الفلترة والترتيب</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card border-success">
                    <div class="card-header bg-success text-white">
                      <h6 class="mb-0">المرحلة الثالثة (أسبوع 4-6)</h6>
                    </div>
                    <div class="card-body">
                      <ul class="list-unstyled small">
                        <li>✓ نظام التقييم</li>
                        <li>✓ الإحصائيات والتقارير</li>
                        <li>✓ الميزات المتقدمة</li>
                        <li>✓ الاختبار الشامل</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>

<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Tasks API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-put {
        background: #ffc107;
        color: #212529;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      .auth-required {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .auth-icon {
        color: #856404;
        margin-left: 10px;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 2px;
      }

      .status-pending {
        background: #fff3cd;
        color: #856404;
      }
      .status-accepted {
        background: #d4edda;
        color: #155724;
      }
      .status-in-progress {
        background: #cce5ff;
        color: #004085;
      }
      .status-completed {
        background: #d1ecf1;
        color: #0c5460;
      }
      .status-cancelled {
        background: #f8d7da;
        color: #721c24;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Tasks API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-tasks"></i> Driver Tasks API</h1>
        <p>إدارة المهام المخصصة للسائق وتتبع حالتها</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <div class="auth-required">
        <i class="fas fa-lock auth-icon"></i>
        <strong>مصادقة مطلوبة:</strong> جميع endpoints في هذا القسم تتطلب Bearer Token في header للمصادقة.
      </div>

      <div class="note">
        <i class="fas fa-info-circle note-icon"></i>
        <strong>حالات المهام:</strong>
        <div style="margin-top: 10px">
          <span class="status-badge status-pending">pending</span> - مهام في انتظار القبول
          <span class="status-badge status-accepted">accepted</span> - مهام مقبولة
          <span class="status-badge status-in-progress">in_progress</span> - مهام قيد التنفيذ
          <span class="status-badge status-completed">completed</span> - مهام مكتملة
          <span class="status-badge status-cancelled">cancelled</span> - مهام ملغية
        </div>
      </div>

      <!-- Get Tasks Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">الحصول على المهام</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/tasks</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على قائمة المهام المخصصة للسائق مع إمكانية التصفية حسب الحالة والترقيم.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات الاستعلام</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>status</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>تصفية حسب الحالة (pending, accepted, in_progress, completed, cancelled)</td>
              </tr>
              <tr>
                <td><code>page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رقم الصفحة (افتراضي: 1)</td>
              </tr>
              <tr>
                <td><code>per_page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>عدد العناصر في الصفحة (1-50، افتراضي: 10)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Tasks retrieved successfully",
    "data": {
        "tasks": [
            {
                "id": 123,
                "total_price": 150.00,
                "commission": 22.50,
                "customer_name": "محمد أحمد",
                "pickup_address": "الرياض، حي النرجس",
                "delivery_address": "الرياض، حي الملز",
                "status": "accepted",
                "driver_id": 1,
                "pending_driver_id": null,
                "created_at": "2024-01-15T10:30:00.000000Z",
                "pickup_point": {
                    "id": 1,
                    "address": "الرياض، حي النرجس",
                    "latitude": 24.7136,
                    "longitude": 46.6753,
                    "contact_name": "محمد أحمد",
                    "contact_phone": "966501234567"
                },
                "delivery_point": {
                    "id": 2,
                    "address": "الرياض، حي الملز",
                    "latitude": 24.6877,
                    "longitude": 46.7219,
                    "contact_name": "سارة محمد",
                    "contact_phone": "966501234568"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 5,
            "per_page": 10,
            "total": 47,
            "from": 1,
            "to": 10
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Get Task Details Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">تفاصيل المهمة</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/tasks/{taskId}</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على تفاصيل مهمة محددة مع جميع المعلومات المرتبطة بها.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>taskId</code></td>
                <td>integer</td>
                <td>معرف المهمة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Task details retrieved successfully",
    "data": {
        "task": {
            "id": 123,
            "customer_id": 45,
            "driver_id": 1,
            "pickup_point_id": 1,
            "delivery_point_id": 2,
            "vehicle_size_id": 2,
            "status": "accepted",
            "total_price": 150.00,
            "commission": 22.50,
            "driver_earnings": 127.50,
            "description": "نقل أثاث منزلي",
            "special_instructions": "التعامل بحذر مع الأثاث",
            "estimated_duration": 120,
            "actual_duration": null,
            "pickup_time": "2024-01-15T14:00:00.000000Z",
            "delivery_time": null,
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T11:00:00.000000Z",
            "customer": {
                "id": 45,
                "name": "محمد أحمد",
                "phone": "966501234567",
                "email": "<EMAIL>"
            },
            "pickup_point": {
                "id": 1,
                "address": "الرياض، حي النرجس، شارع الأمير محمد بن عبدالعزيز",
                "latitude": 24.7136,
                "longitude": 46.6753,
                "contact_name": "محمد أحمد",
                "contact_phone": "966501234567",
                "notes": "الشقة رقم 15، الدور الثالث"
            },
            "delivery_point": {
                "id": 2,
                "address": "الرياض، حي الملز، شارع الملك فهد",
                "latitude": 24.6877,
                "longitude": 46.7219,
                "contact_name": "سارة محمد",
                "contact_phone": "966501234568",
                "notes": "فيلا رقم 25، بجانب المسجد"
            },
            "vehicle_size": {
                "id": 2,
                "name": "3 طن",
                "description": "شاحنة متوسطة الحجم"
            },
            "task_history": [
                {
                    "id": 1,
                    "status": "created",
                    "notes": "تم إنشاء المهمة",
                    "created_at": "2024-01-15T10:30:00.000000Z"
                },
                {
                    "id": 2,
                    "status": "accepted",
                    "notes": "تم قبول المهمة من قبل السائق",
                    "created_at": "2024-01-15T11:00:00.000000Z"
                }
            ]
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Accept Task Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">قبول المهمة</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/tasks/{taskId}/accept</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يقبل السائق مهمة معينة ويحدث حالتها إلى "accepted".</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>taskId</code></td>
                <td>integer</td>
                <td>معرف المهمة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Task accepted successfully",
    "data": {
        "task": {
            "id": 123,
            "status": "accepted",
            "driver_id": 1,
            "accepted_at": "2024-01-15T11:00:00.000000Z"
        }
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-exclamation-triangle"></i> أخطاء محتملة</h3>

          <div class="response-example">
            <span class="status-code status-400">400 Bad Request</span>
            <strong>المهمة غير متاحة للقبول:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Task is not available for acceptance"
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-404">404 Not Found</span>
            <strong>المهمة غير موجودة:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Task not found"
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Update Task Status Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-put">PUT</span>
          <h2 class="method-title">تحديث حالة المهمة</h2>
        </div>

        <div class="endpoint"><strong>PUT</strong> /api/driver/tasks/{taskId}/status</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحدث حالة المهمة إلى الحالة التالية في دورة حياة المهمة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>status</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>الحالة الجديدة (started, picked_up, in_transit, delivered)</td>
              </tr>
              <tr>
                <td><code>notes</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>ملاحظات إضافية</td>
              </tr>
              <tr>
                <td><code>latitude</code></td>
                <td>float</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>خط العرض الحالي</td>
              </tr>
              <tr>
                <td><code>longitude</code></td>
                <td>float</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>خط الطول الحالي</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "status": "picked_up",
    "notes": "تم استلام البضاعة بنجاح",
    "latitude": 24.7136,
    "longitude": 46.6753
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Task status updated successfully",
    "data": {
        "task": {
            "id": 123,
            "status": "picked_up",
            "updated_at": "2024-01-15T14:30:00.000000Z"
        },
        "history": {
            "id": 3,
            "status": "picked_up",
            "notes": "تم استلام البضاعة بنجاح",
            "latitude": 24.7136,
            "longitude": 46.6753,
            "created_at": "2024-01-15T14:30:00.000000Z"
        }
    }
}</code></pre>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

<!doctype html>
<html dir="rtl" lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تحليل محدث - طلب السحب النقدي لمحفظة الفريق</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap"
      rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <style>
      body {
        font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        min-height: 100vh;
        padding: 20px 0;
      }
      .analysis-container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 30px;
        text-align: center;
      }
      .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
      }
      .content {
        padding: 40px;
      }
      .section-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #28a745;
      }
      .section-card.understanding {
        border-left-color: #007bff;
        background: #e3f2fd;
      }
      .section-card.requirements {
        border-left-color: #28a745;
        background: #e8f5e8;
      }
      .section-card.technical {
        border-left-color: #dc3545;
        background: #f8e8e8;
      }
      .section-card.implementation {
        border-left-color: #6f42c1;
        background: #f3e8ff;
      }
      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 15px 0;
      }
      .database-table {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
      }
      .comparison-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
      }
      .comparison-item {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
      }
      .comparison-item.driver {
        border-color: #007bff;
      }
      .comparison-item.team {
        border-color: #28a745;
      }
      .step-item {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
        position: relative;
      }
      .step-number {
        position: absolute;
        top: -15px;
        right: 20px;
        background: #28a745;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .feature-list {
        list-style: none;
        padding: 0;
      }
      .feature-list li {
        padding: 8px 0;
        position: relative;
        padding-right: 25px;
      }
      .feature-list li::before {
        content: '✓';
        position: absolute;
        right: 0;
        color: #28a745;
        font-weight: bold;
      }
      .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 15px 20px;
      }
      .workflow-diagram {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
      }
      .entity-box {
        display: inline-block;
        background: #28a745;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        margin: 5px;
        font-weight: 600;
      }
      .arrow {
        font-size: 1.5rem;
        color: #6c757d;
        margin: 0 10px;
      }
      .update-badge {
        background: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        margin-right: 10px;
      }
    </style>
  </head>
  <body>
    <div class="analysis-container">
      <div class="header">
        <h1><i class="bi bi-arrow-clockwise me-3"></i>تحليل محدث - طلب السحب النقدي لمحفظة الفريق</h1>
        <p class="mb-0">تقرير مفصل محدث بناءً على توضيحات المستخدم</p>
        <span class="update-badge">محدث</span>
      </div>

      <div class="content">
        <!-- فهم المتطلبات المحدث -->
        <div class="section-card understanding">
          <h4><i class="bi bi-lightbulb-fill text-primary me-2"></i>فهم المتطلبات المحدث</h4>

          <div class="alert alert-success">
            <strong>توضيح المستخدم:</strong> رئيس الفريق يُحدد من جدول user_has_teams - أول مستخدم مرتبط بالفريق هو رئيس
            الفريق
          </div>

          <h6>📋 المطلوب الأساسي:</h6>
          <ul class="feature-list">
            <li>تطبيق نظام طلب السحب النقدي على محفظة الفريق (team_wallet)</li>
            <li>إضافة الخيار في صفحة views/admin/teams/wallets/index.blade.php</li>
            <li>استخدام بيانات أول مستخدم في user_has_teams كرئيس فريق</li>
            <li>تصميم مطابق لنظام محفظة السائق</li>
            <li>إنشاء جدول logs منفصل لمحفظة الفريق</li>
            <li><strong>شرط مهم:</strong> منع إنشاء طلب سحب إذا لم يملك الفريق رئيساً</li>
          </ul>

          <h6>🔍 المقارنة المحدثة:</h6>
          <div class="comparison-grid">
            <div class="comparison-item driver">
              <h6>محفظة السائق (موجودة)</h6>
              <ul class="feature-list">
                <li>جدول: wallets</li>
                <li>معاملات: wallet_transactions</li>
                <li>سجلات: wallet_payment_request_logs</li>
                <li>البيانات: من Driver model</li>
                <li>البنك: driver->bank_name, account_number, iban_number</li>
              </ul>
            </div>
            <div class="comparison-item team">
              <h6>محفظة الفريق (مطلوبة)</h6>
              <ul class="feature-list">
                <li>جدول: team_wallet (موجود)</li>
                <li>معاملات: team_wallet_transactions (موجود)</li>
                <li>سجلات: team_wallet_payment_request_logs (جديد)</li>
                <li>البيانات: من أول User في user_has_teams</li>
                <li>البنك: user->bank_name, account_number, iban_number</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- آلية تحديد رئيس الفريق -->
        <div class="section-card requirements">
          <h4><i class="bi bi-person-check-fill text-success me-2"></i>آلية تحديد رئيس الفريق</h4>

          <h6>🔍 الهيكل الحالي:</h6>
          <div class="code-block">
            // جدول user_has_teams Schema::create('user_has_teams', function (Blueprint $table) { $table->id(); // يوفر
            ترتيب تلقائي حسب وقت الإدراج $table->unsignedBigInteger('user_id'); $table->unsignedBigInteger('team_id');
            // Foreign keys... }); // العلاقة في Teams model public function users() { return
            $this->hasMany(User_Teams::class, 'team_id')->with('user'); }
          </div>

          <h6>✅ الحل المطبق:</h6>
          <div class="step-item">
            <div class="step-number">1</div>
            <h6>إضافة method لرئيس الفريق</h6>
            <div class="code-block">
              // في app/Models/Teams.php public function getTeamLeaderAttribute() { $firstUserTeam =
              $this->users()->orderBy('id', 'asc')->first(); return $firstUserTeam ? $firstUserTeam->user : null; }
              public function hasTeamLeader() { return $this->users()->exists(); }
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <h6>التحقق من وجود رئيس فريق</h6>
            <div class="code-block">
              // في Controller - التحقق قبل السماح بطلب السحب if (!$team->hasTeamLeader()) { return response()->json([
              'status' => 0, 'error' => 'لا يمكن إنشاء طلب سحب نقدي - الفريق لا يملك رئيس فريق' ]); } $teamLeader =
              $team->teamLeader; if (!$teamLeader->bank_name || !$teamLeader->account_number ||
              !$teamLeader->iban_number) { return response()->json([ 'status' => 0, 'error' => 'بيانات البنك غير مكتملة
              لرئيس الفريق' ]); }
            </div>
          </div>
        </div>

        <!-- الهيكل التقني -->
        <div class="section-card technical">
          <h4><i class="bi bi-gear-fill text-danger me-2"></i>الهيكل التقني المطلوب</h4>

          <h6>🗄️ جدول السجلات الجديد:</h6>
          <div class="database-table">
            <strong>team_wallet_payment_request_logs</strong>
            <div class="code-block">
              Schema::create('team_wallet_payment_request_logs', function (Blueprint $table) { $table->id();
              $table->unsignedBigInteger('team_wallet_id'); $table->unsignedBigInteger('user_id'); // المستخدم الذي طبع
              الطلب $table->unsignedBigInteger('team_id'); // الفريق $table->unsignedBigInteger('team_leader_id'); //
              رئيس الفريق $table->decimal('amount', 10, 2); $table->text('notes')->nullable();
              $table->string('ip_address', 45)->nullable(); $table->timestamp('printed_at'); $table->timestamps(); //
              Foreign Keys $table->foreign('team_wallet_id')->references('id')->on('team_wallet');
              $table->foreign('user_id')->references('id')->on('users');
              $table->foreign('team_id')->references('id')->on('teams');
              $table->foreign('team_leader_id')->references('id')->on('users'); // Indexes للأداء
              $table->index(['team_wallet_id', 'printed_at']); $table->index(['team_id', 'printed_at']);
              $table->index(['team_leader_id', 'printed_at']); });
            </div>
          </div>

          <h6>📊 النموذج المطلوب:</h6>
          <div class="code-block">
            // app/Models/TeamWalletPaymentRequestLog.php class TeamWalletPaymentRequestLog extends Model { protected
            $fillable = [ 'team_wallet_id', 'user_id', 'team_id', 'team_leader_id', 'amount', 'notes', 'ip_address',
            'printed_at' ]; protected $casts = [ 'amount' => 'decimal:2', 'printed_at' => 'datetime', ]; // العلاقات
            public function teamWallet() { return $this->belongsTo(Team_Wallet::class); } public function user() {
            return $this->belongsTo(User::class); } public function team() { return $this->belongsTo(Teams::class); }
            public function teamLeader() { return $this->belongsTo(User::class, 'team_leader_id'); } // Scopes public
            function scopeForTeamWallet($query, $teamWalletId) { return $query->where('team_wallet_id', $teamWalletId);
            } }
          </div>
        </div>

        <!-- خطة التنفيذ المفصلة -->
        <div class="section-card implementation">
          <h4><i class="bi bi-list-check text-primary me-2"></i>خطة التنفيذ المفصلة</h4>

          <h6>🎯 المرحلة الأولى: إعداد قاعدة البيانات</h6>
          <div class="step-item">
            <div class="step-number">1</div>
            <h6>تحديث Teams Model</h6>
            <p>إضافة methods للحصول على رئيس الفريق والتحقق من وجوده</p>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <h6>إنشاء Migration للسجلات</h6>
            <p>إنشاء team_wallet_payment_request_logs table</p>
          </div>

          <div class="step-item">
            <div class="step-number">3</div>
            <h6>إنشاء النموذج</h6>
            <p>إنشاء TeamWalletPaymentRequestLog model مع العلاقات</p>
          </div>

          <h6>🎯 المرحلة الثانية: Backend Development</h6>
          <div class="step-item">
            <div class="step-number">4</div>
            <h6>تحديث TeamWalletController</h6>
            <p>إضافة methods للتعامل مع طلبات السحب والسجلات</p>
          </div>

          <div class="step-item">
            <div class="step-number">5</div>
            <h6>إضافة Routes</h6>
            <p>إضافة routes للعمليات الجديدة</p>
          </div>

          <h6>🎯 المرحلة الثالثة: Frontend Development</h6>
          <div class="step-item">
            <div class="step-number">6</div>
            <h6>تحديث Blade Template</h6>
            <p>إضافة قسم طلب السحب في views/admin/teams/wallets/index.blade.php</p>
          </div>

          <div class="step-item">
            <div class="step-number">7</div>
            <h6>تحديث JavaScript</h6>
            <p>إضافة functions في resources/js/admin/teams/wallet.js</p>
          </div>

          <div class="step-item">
            <div class="step-number">8</div>
            <h6>إنشاء PDF Template</h6>
            <p>إنشاء views/admin/teams/wallets/payment-request-pdf.blade.php</p>
          </div>
        </div>

        <!-- التوصية النهائية المحدثة -->
        <div class="alert alert-success alert-custom">
          <h5><i class="bi bi-check-circle-fill me-2"></i>التوصية النهائية المحدثة</h5>
          <p class="mb-3">بناءً على التوضيح الجديد، سيتم تطبيق النهج التالي:</p>
          <ul class="feature-list">
            <li><strong>استخدام أول مستخدم</strong> في user_has_teams كرئيس فريق</li>
            <li><strong>التحقق من وجود رئيس فريق</strong> قبل السماح بطلب السحب</li>
            <li><strong>استخدام البيانات البنكية</strong> لرئيس الفريق من جدول users</li>
            <li><strong>تطبيق نفس التصميم</strong> المستخدم في محفظة السائق</li>
            <li><strong>إنشاء جدول logs منفصل</strong> لمحفظة الفريق</li>
          </ul>

          <div class="alert alert-warning mt-3">
            <strong>شرط مهم:</strong> إذا لم يملك الفريق أي مستخدمين في user_has_teams، فلن يُسمح بإنشاء طلب سحب نقدي
          </div>

          <p class="mb-0"><strong>هل توافق على هذا التصور المحدث وتريد البدء في التنفيذ؟</strong></p>
        </div>
      </div>
    </div>
  </body>
</html>

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Task_Ad extends Model
{
  protected $table = 'tasks_ads';
  protected $fillable = [
    'description',
    'status',
    'highest_price',
    'lowest_price',
    'included',
    'closed_at',
    'service_commission',
    'service_commission_type',
    'vat_commission',
    'task_id',
  ];

  public function task()
  {
    return $this->belongsTo(Task::class, 'task_id');
  }

  public function offers()
  {
    return $this->hasMany(Task_Offire::class, 'task_ad_id');
  }
}

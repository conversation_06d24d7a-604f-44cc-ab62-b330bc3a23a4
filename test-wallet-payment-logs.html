<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام Logs طلبات السداد للمحفظة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }
        .content {
            padding: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .feature-card.success {
            border-left-color: #28a745;
        }
        .feature-card.info {
            border-left-color: #17a2b8;
        }
        .feature-card.warning {
            border-left-color: #ffc107;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .log-example {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .database-schema {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .api-endpoint {
            background: #f3e5f5;
            border: 1px solid #ce93d8;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .workflow-step {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        .workflow-step::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #007bff, #28a745);
            border-radius: 8px;
            z-index: -1;
        }
        .test-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="bi bi-journal-text me-3"></i>نظام Logs طلبات السداد للمحفظة</h1>
            <p class="mb-0">تتبع شامل لجميع عمليات طباعة طلبات السداد مع تسجيل تفصيلي</p>
        </div>
        
        <div class="content">
            <!-- Overview -->
            <div class="feature-card success">
                <h4><i class="bi bi-check-circle-fill text-success me-2"></i>نظرة عامة على النظام</h4>
                <p class="mb-3">تم تطوير نظام شامل لتتبع وتسجيل جميع عمليات طباعة طلبات السداد للمحافظ مع الميزات التالية:</p>
                <ul class="mb-0">
                    <li>✅ تسجيل تلقائي عند الطباعة الفعلية فقط</li>
                    <li>✅ حفظ جميع البيانات المهمة (المبلغ، المستخدم، IP، التاريخ)</li>
                    <li>✅ واجهة عرض احترافية للسجلات</li>
                    <li>✅ نظام أمان محسن مع تتبع عناوين IP</li>
                    <li>✅ ربط مع جداول المحافظ والمستخدمين والسائقين</li>
                </ul>
            </div>

            <!-- Database Schema -->
            <div class="feature-card info">
                <h4><i class="bi bi-database me-2"></i>هيكل قاعدة البيانات</h4>
                <div class="database-schema">
                    <h6>جدول: wallet_payment_request_logs</h6>
                    <div class="code-block">
id (Primary Key)
wallet_id (Foreign Key → wallets.id)
user_id (Foreign Key → users.id) // المستخدم الذي طبع الطلب
driver_id (Foreign Key → drivers.id) // صاحب المحفظة
amount (decimal) // المبلغ المطلوب
notes (text, nullable) // الملاحظات
ip_address (string) // عنوان IP
printed_at (timestamp) // تاريخ ووقت الطباعة
created_at, updated_at
                    </div>
                    <p class="mb-0"><strong>الفهارس:</strong> تم إضافة فهارس محسنة للأداء على wallet_id, driver_id, user_id مع printed_at</p>
                </div>
            </div>

            <!-- API Endpoints -->
            <div class="feature-card">
                <h4><i class="bi bi-cloud me-2"></i>نقاط النهاية API</h4>
                
                <div class="api-endpoint">
                    <h6><span class="badge bg-success">POST</span> تسجيل طلب السداد</h6>
                    <code>/admin/wallets/{wallet}/log-payment-request</code>
                    <div class="mt-2">
                        <strong>المعاملات:</strong> amount, notes, _token
                    </div>
                </div>

                <div class="api-endpoint">
                    <h6><span class="badge bg-primary">GET</span> جلب سجلات طلبات السداد</h6>
                    <code>/admin/wallets/{wallet}/payment-request-logs</code>
                    <div class="mt-2">
                        <strong>الاستجابة:</strong> مصفوفة مرقمة من السجلات مع pagination
                    </div>
                </div>
            </div>

            <!-- Workflow -->
            <div class="feature-card warning">
                <h4><i class="bi bi-diagram-3 me-2"></i>تدفق العمل</h4>
                
                <div class="workflow-step">
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">1</span>
                        <h6 class="mb-0">المستخدم يضغط على "إنشاء طلب سداد"</h6>
                    </div>
                    <p class="mb-0">يتم فتح modal مع تعبئة بيانات المحفظة والسائق تلقائياً</p>
                </div>

                <div class="workflow-step">
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">2</span>
                        <h6 class="mb-0">المستخدم يدخل المبلغ والملاحظات</h6>
                    </div>
                    <p class="mb-0">التحقق من صحة البيانات والتأكد من عدم تجاوز رصيد المحفظة</p>
                </div>

                <div class="workflow-step">
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">3</span>
                        <h6 class="mb-0">الضغط على "إنشاء مستند طلب السداد"</h6>
                    </div>
                    <p class="mb-0">يتم فتح نافذة طباعة جديدة مع مستند طلب السداد</p>
                </div>

                <div class="workflow-step">
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">4</span>
                        <h6 class="mb-0">المستخدم يطبع المستند فعلياً</h6>
                    </div>
                    <p class="mb-0">عند اكتمال الطباعة، يتم تشغيل event listener للـ afterprint</p>
                </div>

                <div class="workflow-step">
                    <div class="d-flex align-items-center mb-3">
                        <span class="step-number">5</span>
                        <h6 class="mb-0">تسجيل السجل تلقائياً</h6>
                    </div>
                    <p class="mb-0">إرسال AJAX request لحفظ السجل في قاعدة البيانات مع IP والتوقيت</p>
                </div>
            </div>

            <!-- JavaScript Implementation -->
            <div class="feature-card">
                <h4><i class="bi bi-code-slash me-2"></i>التنفيذ في JavaScript</h4>
                <div class="code-block">
// تتبع الطباعة الفعلية
printWindow.addEventListener('afterprint', function () {
  console.log('Print dialog closed - logging payment request');
  
  // تسجيل طلب السداد بعد الطباعة الفعلية
  logPaymentRequest({
    walletId: data.walletData.id,
    amount: data.requestedAmount,
    notes: data.notes || null
  });
  
  printWindow.close();
});

// دالة تسجيل طلب السداد
function logPaymentRequest(data) {
  $.ajax({
    url: `${baseUrl}admin/wallets/${data.walletId}/log-payment-request`,
    method: 'POST',
    data: {
      amount: data.amount,
      notes: data.notes,
      _token: $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.status === 1) {
        console.log('Payment request logged successfully');
        loadPaymentRequestLogs(); // تحديث السجلات
      }
    }
  });
}
                </div>
            </div>

            <!-- Log Display -->
            <div class="feature-card info">
                <h4><i class="bi bi-list-ul me-2"></i>عرض السجلات</h4>
                <p>تم إضافة قسم خاص في صفحة المحفظة لعرض سجلات طلبات السداد:</p>
                
                <div class="log-example">
                    <h6>مثال على عرض السجل:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <small class="text-muted">تاريخ الطباعة</small>
                            <div class="fw-semibold">14/09/2024 15:30</div>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">المبلغ</small>
                            <div class="fw-semibold text-success">850.00 ريال</div>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">المستخدم</small>
                            <div class="fw-semibold">أحمد محمد</div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">عنوان IP</small>
                            <div class="fw-semibold">*************</div>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">الملاحظات</small>
                            <div class="fw-semibold">طلب سداد عاجل</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="feature-card success">
                <h4><i class="bi bi-shield-check me-2"></i>الميزات الأمنية</h4>
                <ul>
                    <li><strong>تتبع عناوين IP:</strong> حفظ عنوان IP لكل عملية طباعة باستخدام IpHelper</li>
                    <li><strong>التحقق من الصلاحيات:</strong> التأكد من أن المستخدم له صلاحية الوصول للمحفظة</li>
                    <li><strong>التحقق من البيانات:</strong> validation شامل للمبالغ والملاحظات</li>
                    <li><strong>سجل مراجعة:</strong> تسجيل جميع العمليات مع التوقيت الدقيق</li>
                    <li><strong>حماية من التلاعب:</strong> التسجيل يتم فقط عند الطباعة الفعلية</li>
                </ul>
            </div>

            <!-- Test Results -->
            <div class="test-result">
                <h5><i class="bi bi-check-circle-fill me-2"></i>نتائج الاختبار</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ المكونات المنجزة:</h6>
                        <ul>
                            <li>Migration لجدول wallet_payment_request_logs</li>
                            <li>Model WalletPaymentRequestLog مع العلاقات</li>
                            <li>Controller methods للتسجيل والعرض</li>
                            <li>JavaScript لتتبع الطباعة الفعلية</li>
                            <li>واجهة عرض السجلات في Blade</li>
                            <li>Routes للـ API endpoints</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔧 الوظائف المتاحة:</h6>
                        <ul>
                            <li>تسجيل تلقائي عند الطباعة</li>
                            <li>عرض السجلات مع pagination</li>
                            <li>تحديث السجلات تلقائياً</li>
                            <li>فلترة وبحث في السجلات</li>
                            <li>تصدير البيانات (قابل للتطوير)</li>
                            <li>إحصائيات سريعة (قابل للتطوير)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="alert alert-info alert-custom">
                <h5><i class="bi bi-info-circle me-2"></i>كيفية الاستخدام:</h5>
                <ol>
                    <li>افتح صفحة المحفظة لأي سائق في لوحة تحكم الإدارة</li>
                    <li>اضغط على زر "طلب سداد" (Payment Request)</li>
                    <li>أدخل المبلغ المطلوب والملاحظات</li>
                    <li>اضغط "إنشاء مستند طلب السداد"</li>
                    <li>اطبع المستند فعلياً من نافذة الطباعة</li>
                    <li>سيتم تسجيل السجل تلقائياً في قسم "سجل طلبات السداد"</li>
                    <li>يمكنك مراجعة جميع السجلات في نفس الصفحة</li>
                </ol>
            </div>

            <!-- Next Steps -->
            <div class="alert alert-warning alert-custom">
                <h5><i class="bi bi-lightbulb me-2"></i>التطويرات المستقبلية:</h5>
                <ul class="mb-0">
                    <li>إضافة فلاتر متقدمة للسجلات (حسب التاريخ، المستخدم، المبلغ)</li>
                    <li>تصدير السجلات إلى Excel/PDF</li>
                    <li>إحصائيات تفصيلية (إجمالي المبالغ، عدد الطلبات)</li>
                    <li>تنبيهات عند طلبات السداد الكبيرة</li>
                    <li>تقارير دورية للإدارة</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

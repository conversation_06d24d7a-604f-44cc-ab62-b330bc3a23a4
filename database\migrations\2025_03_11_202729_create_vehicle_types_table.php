<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('vehicle_types', function (Blueprint $table) {
      $table->id();
      $table->string('name');
      $table->string('en_name');
      $table->unsignedBigInteger('vehicle_id');
      $table->foreign('vehicle_id')->references('id')->on('vehicles')->onDelete('restrict');
      $table->timestamps();

      $table->unique(['name', 'vehicle_id']);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('vehicle_types');
  }
};

<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تقرير مفصل - نظام إدارة الفرق SafeDest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
      }
      .container {
        max-width: 1400px;
      }
      .main-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        text-align: center;
      }
      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
      }
      .nav-tabs {
        border-bottom: 3px solid #667eea;
        background: #f8f9fa;
        padding: 0 20px;
      }
      .nav-tabs .nav-link {
        border: none;
        color: #666;
        font-weight: 600;
        padding: 15px 25px;
        margin: 0 5px;
        border-radius: 10px 10px 0 0;
      }
      .nav-tabs .nav-link.active {
        background: #667eea;
        color: white;
      }
      .tab-content {
        padding: 30px;
      }
      .section-card {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 5px solid #667eea;
      }
      .section-title {
        color: #667eea;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 15px;
      }
      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 10px;
        margin: 15px 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
      }
      .table-custom {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }
      .table-custom th {
        background: #667eea;
        color: white;
        border: none;
        padding: 15px;
      }
      .table-custom td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
      }
      .badge-custom {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
      }
      .status-active {
        background: #28a745;
        color: white;
      }
      .status-inactive {
        background: #dc3545;
        color: white;
      }
      .status-pending {
        background: #ffc107;
        color: #212529;
      }
      .workflow-step {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        border-left: 4px solid #28a745;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }
      .workflow-number {
        background: #667eea;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 15px;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }
      .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-top: 4px solid #667eea;
      }
      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
      }
      .feature-list {
        list-style: none;
        padding: 0;
      }
      .feature-list li {
        background: white;
        margin: 8px 0;
        padding: 12px 15px;
        border-radius: 8px;
        border-right: 4px solid #28a745;
      }
      .feature-list li:before {
        content: '✅';
        margin-left: 10px;
      }
      .relationship-diagram {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        text-align: center;
      }
      .table-box {
        display: inline-block;
        background: #667eea;
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px;
        min-width: 150px;
      }
      .arrow {
        font-size: 1.5rem;
        color: #667eea;
        margin: 0 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main-card">
        <div class="header">
          <h1><i class="fas fa-users-cog"></i> تقرير مفصل - نظام إدارة الفرق</h1>
          <p>تحليل شامل لحالة قاعدة البيانات والوظائف والآليات - SafeDest Platform</p>
          <small>تاريخ التقرير: <?php echo date('Y-m-d H:i:s'); ?></small>
        </div>

        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
          <li class="nav-item">
            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#database">قاعدة البيانات</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#teams">جدول الفرق</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#drivers">جدول السائقين</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#tasks">جدول المهام</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#new-tables">الجداول الجديدة</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#controllers">Controllers</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#workflow">آلية العمل</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#scenarios">السيناريوهات</button>
          </li>
        </ul>

        <div class="tab-content">
          <!-- Database Overview -->
          <div class="tab-pane fade show active" id="database">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">5</div>
                <div>جداول محدثة</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">2</div>
                <div>جداول جديدة</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">15+</div>
                <div>حقول جديدة</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">8</div>
                <div>علاقات جديدة</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">12</div>
                <div>فهارس جديدة</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">3</div>
                <div>ملفات Migration</div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">نظرة عامة على التحديثات</h3>
              <div class="relationship-diagram">
                <div class="table-box">Teams</div>
                <span class="arrow">→</span>
                <div class="table-box">TeamTaskAssignments</div>
                <span class="arrow">→</span>
                <div class="table-box">Tasks</div>
                <br /><br />
                <div class="table-box">Drivers</div>
                <span class="arrow">→</span>
                <div class="table-box">TeamPerformanceLogs</div>
                <span class="arrow">→</span>
                <div class="table-box">Performance Metrics</div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">ملفات Migration المنشأة</h3>
              <div class="table-responsive">
                <table class="table table-custom">
                  <thead>
                    <tr>
                      <th>اسم الملف</th>
                      <th>الغرض</th>
                      <th>الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>2024_12_02_000001_create_team_task_assignments_table.php</code></td>
                      <td>إنشاء جدول تعيينات المهام للفرق</td>
                      <td><span class="badge-custom status-active">مطبق</span></td>
                    </tr>
                    <tr>
                      <td><code>2024_12_02_000002_create_team_performance_logs_table.php</code></td>
                      <td>إنشاء جدول سجلات أداء الفرق</td>
                      <td><span class="badge-custom status-active">مطبق</span></td>
                    </tr>
                    <tr>
                      <td><code>2024_12_02_000003_add_team_management_fields_to_existing_tables.php</code></td>
                      <td>إضافة حقول إدارة الفرق للجداول الموجودة</td>
                      <td><span class="badge-custom status-active">مطبق</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Teams Table -->
          <div class="tab-pane fade" id="teams">
            <div class="section-card">
              <h3 class="section-title">جدول الفرق (teams) - الحقول الأصلية</h3>
              <div class="table-responsive">
                <table class="table table-custom">
                  <thead>
                    <tr>
                      <th>اسم الحقل</th>
                      <th>النوع</th>
                      <th>الوصف</th>
                      <th>القيود</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>id</code></td>
                      <td>bigint</td>
                      <td>المعرف الأساسي</td>
                      <td>Primary Key, Auto Increment, Starting: 10000</td>
                    </tr>
                    <tr>
                      <td><code>name</code></td>
                      <td>string</td>
                      <td>اسم الفريق</td>
                      <td>Unique, Required</td>
                    </tr>
                    <tr>
                      <td><code>address</code></td>
                      <td>text</td>
                      <td>عنوان الفريق</td>
                      <td>Nullable</td>
                    </tr>
                    <tr>
                      <td><code>note</code></td>
                      <td>text</td>
                      <td>ملاحظات</td>
                      <td>Nullable</td>
                    </tr>
                    <tr>
                      <td><code>team_commission_type</code></td>
                      <td>enum</td>
                      <td>نوع عمولة الفريق</td>
                      <td>rate, fixed, subscription</td>
                    </tr>
                    <tr>
                      <td><code>team_commission_value</code></td>
                      <td>decimal(10,2)</td>
                      <td>قيمة عمولة الفريق</td>
                      <td>Nullable</td>
                    </tr>
                    <tr>
                      <td><code>location_update_interval</code></td>
                      <td>integer</td>
                      <td>فترة تحديث الموقع (ثانية)</td>
                      <td>Default: 30</td>
                    </tr>
                    <tr>
                      <td><code>is_public</code></td>
                      <td>boolean</td>
                      <td>هل الفريق عام؟</td>
                      <td>Default: false</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Drivers Table -->
              <div class="tab-pane fade" id="drivers">
                <div class="section-card">
                  <h3 class="section-title">جدول السائقين (drivers) - الحقول الجديدة المضافة</h3>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>اسم الحقل</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                          <th>القيم المسموحة</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>availability_status</code></td>
                          <td>enum</td>
                          <td>حالة توفر السائق</td>
                          <td>available, busy, offline, break</td>
                        </tr>
                        <tr>
                          <td><code>last_activity_at</code></td>
                          <td>timestamp</td>
                          <td>آخر نشاط للسائق</td>
                          <td>Nullable, Indexed</td>
                        </tr>
                        <tr>
                          <td><code>performance_metrics</code></td>
                          <td>json</td>
                          <td>مقاييس أداء السائق</td>
                          <td>JSON object with performance data</td>
                        </tr>
                        <tr>
                          <td><code>max_concurrent_tasks</code></td>
                          <td>integer</td>
                          <td>الحد الأقصى للمهام المتزامنة</td>
                          <td>Default: 1</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div class="section-card">
                  <h3 class="section-title">حالات توفر السائق</h3>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <div class="d-flex align-items-center">
                          <div class="workflow-number">1</div>
                          <div>
                            <h5><span class="badge-custom status-active">Available</span></h5>
                            <p>السائق متاح لاستقبال مهام جديدة</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <div class="d-flex align-items-center">
                          <div class="workflow-number">2</div>
                          <div>
                            <h5><span class="badge-custom status-pending">Busy</span></h5>
                            <p>السائق مشغول بتنفيذ مهمة حالياً</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <div class="d-flex align-items-center">
                          <div class="workflow-number">3</div>
                          <div>
                            <h5><span class="badge-custom status-inactive">Offline</span></h5>
                            <p>السائق غير متصل أو غير متاح</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <div class="d-flex align-items-center">
                          <div class="workflow-number">4</div>
                          <div>
                            <h5><span class="badge-custom" style="background: #17a2b8; color: white">Break</span></h5>
                            <p>السائق في استراحة مؤقتة</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="section-card">
                  <h3 class="section-title">مقاييس الأداء (Performance Metrics)</h3>
                  <div class="code-block">
                    { "total_tasks_completed": 150, "average_completion_time": 45.5, "customer_rating_average": 4.7,
                    "on_time_delivery_rate": 92.3, "distance_efficiency": 85.2, "fuel_consumption_avg": 8.5,
                    "last_performance_update": "2024-12-02T10:30:00Z" }
                  </div>
                </div>
              </div>

              <!-- Tasks Table -->
              <div class="tab-pane fade" id="tasks">
                <div class="section-card">
                  <h3 class="section-title">جدول المهام (tasks) - الحقول الجديدة المضافة</h3>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>اسم الحقل</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                          <th>الغرض</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>assigned_by_team</code></td>
                          <td>foreignId</td>
                          <td>معرف الفريق المُعيِّن</td>
                          <td>ربط المهمة بالفريق الذي قام بتعيينها</td>
                        </tr>
                        <tr>
                          <td><code>team_assigned_at</code></td>
                          <td>timestamp</td>
                          <td>وقت تعيين الفريق</td>
                          <td>تسجيل وقت تعيين المهمة للفريق</td>
                        </tr>
                        <tr>
                          <td><code>delivery_number</code></td>
                          <td>string</td>
                          <td>رقم التسليم</td>
                          <td>رقم مرجعي للتسليم (مضاف مسبقاً)</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div class="section-card">
                  <h3 class="section-title">العلاقات الجديدة لجدول المهام</h3>
                  <ul class="feature-list">
                    <li><strong>assignedByTeam():</strong> belongsTo → Teams (الفريق المُعيِّن)</li>
                    <li><strong>teamAssignment():</strong> hasOne → TeamTaskAssignment (تعيين الفريق)</li>
                    <li><strong>performanceLogs():</strong> hasMany → TeamPerformanceLog (سجلات الأداء)</li>
                  </ul>
                </div>

                <div class="section-card">
                  <h3 class="section-title">دورة حياة المهمة مع الفرق</h3>
                  <div class="workflow-step">
                    <div class="d-flex align-items-center">
                      <div class="workflow-number">1</div>
                      <div>
                        <h5>إنشاء المهمة</h5>
                        <p>يتم إنشاء المهمة بحالة 'pending' بدون تعيين فريق</p>
                      </div>
                    </div>
                  </div>
                  <div class="workflow-step">
                    <div class="d-flex align-items-center">
                      <div class="workflow-number">2</div>
                      <div>
                        <h5>تعيين الفريق</h5>
                        <p>يتم تعيين المهمة لفريق معين وتسجيل الوقت في team_assigned_at</p>
                      </div>
                    </div>
                  </div>
                  <div class="workflow-step">
                    <div class="d-flex align-items-center">
                      <div class="workflow-number">3</div>
                      <div>
                        <h5>تعيين السائق</h5>
                        <p>يقوم الفريق بتعيين المهمة لسائق متاح من أعضاء الفريق</p>
                      </div>
                    </div>
                  </div>
                  <div class="workflow-step">
                    <div class="d-flex align-items-center">
                      <div class="workflow-number">4</div>
                      <div>
                        <h5>تنفيذ المهمة</h5>
                        <p>يقوم السائق بتنفيذ المهمة وتسجيل بيانات الأداء</p>
                      </div>
                    </div>
                  </div>
                  <div class="workflow-step">
                    <div class="d-flex align-items-center">
                      <div class="workflow-number">5</div>
                      <div>
                        <h5>تسجيل الأداء</h5>
                        <p>يتم تسجيل بيانات الأداء في TeamPerformanceLog</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- New Tables -->
              <div class="tab-pane fade" id="new-tables">
                <div class="section-card">
                  <h3 class="section-title">جدول تعيينات المهام للفرق (team_task_assignments)</h3>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>اسم الحقل</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                          <th>القيود</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>id</code></td>
                          <td>bigint</td>
                          <td>المعرف الأساسي</td>
                          <td>Primary Key, Auto Increment</td>
                        </tr>
                        <tr>
                          <td><code>team_id</code></td>
                          <td>foreignId</td>
                          <td>معرف الفريق</td>
                          <td>FK → teams.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>task_id</code></td>
                          <td>foreignId</td>
                          <td>معرف المهمة</td>
                          <td>FK → tasks.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>assigned_by</code></td>
                          <td>foreignId</td>
                          <td>معرف المُعيِّن</td>
                          <td>FK → users.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>assigned_at</code></td>
                          <td>timestamp</td>
                          <td>وقت التعيين</td>
                          <td>Default: CURRENT_TIMESTAMP</td>
                        </tr>
                        <tr>
                          <td><code>status</code></td>
                          <td>enum</td>
                          <td>حالة التعيين</td>
                          <td>pending, accepted, rejected, completed</td>
                        </tr>
                        <tr>
                          <td><code>notes</code></td>
                          <td>text</td>
                          <td>ملاحظات</td>
                          <td>Nullable</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="mt-3">
                    <h5>الفهارس والقيود:</h5>
                    <ul class="feature-list">
                      <li>فهرس مركب على (team_id, status) لتحسين الاستعلامات</li>
                      <li>فهرس على task_id لتحسين البحث</li>
                      <li>فهرس على assigned_at للترتيب الزمني</li>
                      <li>قيد فريد على (team_id, task_id) لمنع التعيين المكرر</li>
                    </ul>
                  </div>
                </div>

                <div class="section-card">
                  <h3 class="section-title">جدول سجلات أداء الفرق (team_performance_logs)</h3>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>اسم الحقل</th>
                          <th>النوع</th>
                          <th>الوصف</th>
                          <th>القيود</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>id</code></td>
                          <td>bigint</td>
                          <td>المعرف الأساسي</td>
                          <td>Primary Key, Auto Increment</td>
                        </tr>
                        <tr>
                          <td><code>team_id</code></td>
                          <td>foreignId</td>
                          <td>معرف الفريق</td>
                          <td>FK → teams.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>driver_id</code></td>
                          <td>foreignId</td>
                          <td>معرف السائق</td>
                          <td>FK → drivers.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>task_id</code></td>
                          <td>foreignId</td>
                          <td>معرف المهمة</td>
                          <td>FK → tasks.id, CASCADE</td>
                        </tr>
                        <tr>
                          <td><code>performance_score</code></td>
                          <td>decimal(5,2)</td>
                          <td>نقاط الأداء</td>
                          <td>من 0 إلى 100</td>
                        </tr>
                        <tr>
                          <td><code>completion_time_minutes</code></td>
                          <td>integer</td>
                          <td>وقت الإنجاز بالدقائق</td>
                          <td>Nullable</td>
                        </tr>
                        <tr>
                          <td><code>distance_traveled</code></td>
                          <td>decimal(10,2)</td>
                          <td>المسافة المقطوعة (كم)</td>
                          <td>Nullable</td>
                        </tr>
                        <tr>
                          <td><code>completion_status</code></td>
                          <td>enum</td>
                          <td>حالة الإنجاز</td>
                          <td>completed, cancelled, failed</td>
                        </tr>
                        <tr>
                          <td><code>performance_metrics</code></td>
                          <td>json</td>
                          <td>مقاييس إضافية</td>
                          <td>JSON object</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div class="section-card">
                  <h3 class="section-title">الغرض من الجداول الجديدة</h3>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <h5>team_task_assignments</h5>
                        <ul class="feature-list">
                          <li>تتبع تعيين المهام للفرق</li>
                          <li>إدارة حالات التعيين</li>
                          <li>تسجيل من قام بالتعيين</li>
                          <li>منع التعيين المكرر</li>
                          <li>تحليل أنماط التعيين</li>
                        </ul>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="workflow-step">
                        <h5>team_performance_logs</h5>
                        <ul class="feature-list">
                          <li>تسجيل أداء السائقين والفرق</li>
                          <li>حساب مقاييس الأداء</li>
                          <li>تحليل الكفاءة</li>
                          <li>إنشاء التقارير</li>
                          <li>تحسين التعيين المستقبلي</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Controllers -->
              <div class="tab-pane fade" id="controllers">
                <div class="section-card">
                  <h3 class="section-title">TeamManagementController</h3>
                  <div class="table-responsive">
                    <table class="table table-custom">
                      <thead>
                        <tr>
                          <th>الدالة</th>
                          <th>الغرض</th>
                          <th>المعاملات</th>
                          <th>الإرجاع</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>index($teamId)</code></td>
                          <td>لوحة إدارة الفريق الرئيسية</td>
                          <td>معرف الفريق</td>
                          <td>View مع الإحصائيات والأنشطة</td>
                        </tr>
                        <tr>
                          <td><code>drivers($teamId)</code></td>
                          <td>صفحة إدارة السائقين</td>
                          <td>معرف الفريق</td>
                          <td>View مع قائمة السائقين</td>
                        </tr>
                        <tr>
                          <td><code>getDriversData($teamId)</code></td>
                          <td>API لبيانات السائقين</td>
                          <td>معرف الفريق + فلاتر</td>
                          <td>JSON مع بيانات السائقين</td>
                        </tr>
                        <tr>
                          <td><code>updateDriverStatus($teamId, $driverId)</code></td>
                          <td>تحديث حالة السائق</td>
                          <td>معرف الفريق والسائق</td>
                          <td>JSON response</td>
                        </tr>
                        <tr>
                          <td><code>tasks($teamId)</code></td>
                          <td>صفحة مهام الفريق</td>
                          <td>معرف الفريق</td>
                          <td>View مع قائمة المهام</td>
                        </tr>
                        <tr>
                          <td><code>getTasksData($teamId)</code></td>
                          <td>API لبيانات المهام</td>
                          <td>معرف الفريق + فلاتر</td>
                          <td>JSON مع بيانات المهام</td>
                        </tr>
                        <tr>
                          <td><code>updateSettings($teamId)</code></td>
                          <td>تحديث إعدادات الفريق</td>
                          <td>معرف الفريق + البيانات</td>
                          <td>JSON response</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

                <!-- Workflow -->
                <div class="tab-pane fade" id="workflow">
                    <div class="section-card">
                        <h3 class="section-title">آلية عمل لوحة تحكم الفريق</h3>
                        <div class="workflow-step">
                            <div class="d-flex align-items-center">
                                <div class="workflow-number">1</div>
                                <div>
                                    <h5>الوصول للوحة التحكم</h5>
                                    <p>يتم الوصول عبر صفحة الفريق → زر "Manage Team" → يؤدي إلى /admin/teams/{teamId}/management</p>
                                </div>
                            </div>
                        </div>
                        <div class="workflow-step">
                            <div class="d-flex align-items-center">
                                <div class="workflow-number">2</div>
                                <div>
                                    <h5>عرض الإحصائيات</h5>
                                    <p>يتم حساب وعرض إحصائيات الفريق: عدد السائقين، المهام النشطة، المهام المكتملة اليوم</p>
                                </div>
                            </div>
                        </div>
                        <div class="workflow-step">
                            <div class="d-flex align-items-center">
                                <div class="workflow-number">3</div>
                                <div>
                                    <h5>إدارة السائقين</h5>
                                    <p>عرض جدول تفاعلي بالسائقين مع إمكانية تحديث الحالة وعرض مقاييس الأداء</p>
                                </div>
                            </div>
                        </div>
                        <div class="workflow-step">
                            <div class="d-flex align-items-center">
                                <div class="workflow-number">4</div>
                                <div>
                                    <h5>تعيين المهام</h5>
                                    <p>نظام ذكي لتعيين المهام يدوياً أو تلقائياً بناءً على معايير محددة</p>
                                </div>
                            </div>
                        </div>
                        <div class="workflow-step">
                            <div class="d-flex align-items-center">
                                <div class="workflow-number">5</div>
                                <div>
                                    <h5>تقارير الأداء</h5>
                                    <p>عرض تقارير شاملة عن أداء الفريق والسائقين مع رسوم بيانية تفاعلية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">خوارزمية التعيين الذكي</h3>
                        <div class="code-block">
1. فحص توافق حجم المركبة مع متطلبات المهمة
2. التحقق من وجود السائق داخل المنطقة الجغرافية المطلوبة
3. فحص حالة توفر السائق (available)
4. التحقق من عدم تجاوز الحد الأقصى للمهام المتزامنة
5. ترتيب السائقين حسب:
   - نقاط الأداء (الأعلى أولاً)
   - المسافة من نقطة الاستلام (الأقرب أولاً)
   - آخر نشاط (الأحدث أولاً)
6. تعيين المهمة للسائق الأنسب
7. تحديث حالة السائق إلى 'busy'
8. تسجيل التعيين في team_task_assignments
                        </div>
                    </div>
                </div>

                <!-- Scenarios -->
                <div class="tab-pane fade" id="scenarios">
                    <div class="section-card">
                        <h3 class="section-title">السيناريوهات المختلفة</h3>

                        <div class="workflow-step">
                            <h5>سيناريو 1: تعيين مهمة جديدة</h5>
                            <ol>
                                <li>يتم إنشاء مهمة جديدة بحالة 'pending'</li>
                                <li>المدير يختار فريق مناسب</li>
                                <li>يتم إنشاء سجل في team_task_assignments</li>
                                <li>الفريق يستقبل إشعار بالمهمة الجديدة</li>
                                <li>مدير الفريق يعين المهمة لسائق متاح</li>
                                <li>يتم تحديث حالة المهمة إلى 'assign'</li>
                            </ol>
                        </div>

                        <div class="workflow-step">
                            <h5>سيناريو 2: التعيين التلقائي</h5>
                            <ol>
                                <li>تفعيل auto_assignment_enabled للفريق</li>
                                <li>تحديد assignment_criteria (أحجام المركبات، المناطق)</li>
                                <li>عند إنشاء مهمة جديدة، يتم فحص الفرق المؤهلة</li>
                                <li>تطبيق خوارزمية التعيين الذكي</li>
                                <li>تعيين المهمة تلقائياً للسائق الأنسب</li>
                                <li>إرسال إشعارات للسائق والفريق</li>
                            </ol>
                        </div>

                        <div class="workflow-step">
                            <h5>سيناريو 3: تسجيل الأداء</h5>
                            <ol>
                                <li>السائق يبدأ تنفيذ المهمة</li>
                                <li>تسجيل وقت البداية والموقع</li>
                                <li>تتبع المسار والمسافة المقطوعة</li>
                                <li>عند إكمال المهمة، حساب مقاييس الأداء</li>
                                <li>إنشاء سجل في team_performance_logs</li>
                                <li>تحديث performance_metrics للسائق</li>
                                <li>تحديث حالة السائق إلى 'available'</li>
                            </ol>
                        </div>

                        <div class="workflow-step">
                            <h5>سيناريو 4: إدارة حالات السائقين</h5>
                            <ul class="feature-list">
                                <li><strong>Available:</strong> السائق متاح ويمكن تعيين مهام جديدة له</li>
                                <li><strong>Busy:</strong> السائق مشغول بمهمة، لا يمكن تعيين مهام إضافية</li>
                                <li><strong>Offline:</strong> السائق غير متصل، يتم استبعاده من التعيين</li>
                                <li><strong>Break:</strong> السائق في استراحة، يتم استبعاده مؤقتاً</li>
                            </ul>
                        </div>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">مقاييس الأداء المحسوبة</h3>
                        <div class="table-responsive">
                            <table class="table table-custom">
                                <thead>
                                    <tr>
                                        <th>المقياس</th>
                                        <th>طريقة الحساب</th>
                                        <th>النطاق</th>
                                        <th>الاستخدام</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>نقاط الأداء</td>
                                        <td>متوسط وزني للعوامل المختلفة</td>
                                        <td>0-100</td>
                                        <td>ترتيب السائقين في التعيين</td>
                                    </tr>
                                    <tr>
                                        <td>وقت الإنجاز</td>
                                        <td>الفرق بين وقت البداية والانتهاء</td>
                                        <td>بالدقائق</td>
                                        <td>تقييم الكفاءة الزمنية</td>
                                    </tr>
                                    <tr>
                                        <td>المسافة المقطوعة</td>
                                        <td>إجمالي المسافة من GPS</td>
                                        <td>بالكيلومتر</td>
                                        <td>حساب كفاءة المسار</td>
                                    </tr>
                                    <tr>
                                        <td>معدل الإنجاز في الوقت</td>
                                        <td>نسبة المهام المنجزة في الوقت المحدد</td>
                                        <td>0-100%</td>
                                        <td>تقييم الالتزام بالمواعيد</td>
                                    </tr>
                                    <tr>
                                        <td>تقييم العملاء</td>
                                        <td>متوسط تقييمات العملاء</td>
                                        <td>1-5 نجوم</td>
                                        <td>قياس رضا العملاء</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                      <th>اسم الحقل</th>
                      <th>النوع</th>
                      <th>الوصف</th>
                      <th>الغرض</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>auto_assignment_enabled</code></td>
                      <td>boolean</td>
                      <td>تفعيل التعيين التلقائي</td>
                      <td>تحديد ما إذا كان الفريق يدعم التعيين التلقائي للمهام</td>
                    </tr>
                    <tr>
                      <td><code>assignment_criteria</code></td>
                      <td>json</td>
                      <td>معايير التعيين</td>
                      <td>تخزين قواعد وشروط تعيين المهام (أحجام المركبات، المناطق الجغرافية)</td>
                    </tr>
                    <tr>
                      <td><code>max_concurrent_tasks</code></td>
                      <td>integer</td>
                      <td>الحد الأقصى للمهام المتزامنة</td>
                      <td>عدد المهام التي يمكن للفريق التعامل معها في نفس الوقت</td>
                    </tr>
                    <tr>
                      <td><code>team_status</code></td>
                      <td>enum</td>
                      <td>حالة الفريق</td>
                      <td>active, inactive, maintenance - لإدارة حالة الفريق</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">العلاقات الجديدة لجدول الفرق</h3>
              <ul class="feature-list">
                <li><strong>taskAssignments():</strong> hasMany → TeamTaskAssignment (تعيينات المهام)</li>
                <li><strong>performanceLogs():</strong> hasMany → TeamPerformanceLog (سجلات الأداء)</li>
                <li><strong>assignedTasks():</strong> hasManyThrough → Task via TeamTaskAssignment (المهام المعينة)</li>
                <li><strong>availableDrivers():</strong> hasMany → Driver where availability_status = 'available'</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تقرير العملاء</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
</head>
<body style="font-family: 'Tajawal', sans-serif;">
    <div class="container mt-5">
        <h2 class="text-center mb-4">اختبار تقرير المهام للعملاء</h2>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-pdf me-2"></i>
                            إنشاء تقرير المهام
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="testReportForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الفترة الزمنية</label>
                                        <input type="text" id="reportDateRange" class="form-control" placeholder="اختر الفترة الزمنية" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">حالة المهمة</label>
                                        <select id="reportStatus" class="form-select">
                                            <option value="">جميع الحالات</option>
                                            <option value="advertised">معلن</option>
                                            <option value="in_progress">قيد التنفيذ</option>
                                            <option value="assign">مُعين</option>
                                            <option value="started">بدأت</option>
                                            <option value="completed">مكتملة</option>
                                            <option value="canceled">ملغية</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">حالة الدفع</label>
                                        <select id="reportPaymentStatus" class="form-select">
                                            <option value="">جميع حالات الدفع</option>
                                            <option value="waiting">في الانتظار</option>
                                            <option value="pending">معلق</option>
                                            <option value="completed">مكتمل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">طريقة الدفع</label>
                                        <select id="reportPaymentMethod" class="form-select">
                                            <option value="">جميع طرق الدفع</option>
                                            <option value="cash">نقدي</option>
                                            <option value="credit">بطاقة ائتمان</option>
                                            <option value="banking">تحويل بنكي</option>
                                            <option value="wallet">محفظة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        سيتم إنشاء تقرير PDF يحتوي على جميع المهام ضمن المعايير المحددة مع الإحصائيات التفصيلية.
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="button" class="btn btn-success btn-lg" id="generateTestReportBtn">
                                    <i class="bi bi-file-earmark-pdf me-2"></i>
                                    إنشاء تقرير تجريبي
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize date range picker
            $('#reportDateRange').daterangepicker({
                opens: 'left',
                locale: {
                    format: 'DD MMM YYYY',
                    cancelLabel: 'إلغاء',
                    applyLabel: 'تطبيق',
                    customRangeLabel: 'فترة مخصصة'
                },
                startDate: moment().startOf('month'),
                endDate: moment().endOf('month'),
                ranges: {
                    'اليوم': [moment(), moment()],
                    'أمس': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'آخر 7 أيام': [moment().subtract(6, 'days'), moment()],
                    'آخر 30 يوم': [moment().subtract(29, 'days'), moment()],
                    'هذا الشهر': [moment().startOf('month'), moment().endOf('month')],
                    'الشهر الماضي': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            // Handle test report generation
            $('#generateTestReportBtn').on('click', function() {
                const dateRange = $('#reportDateRange').data('daterangepicker');
                const startDate = dateRange.startDate.format('YYYY-MM-DD');
                const endDate = dateRange.endDate.format('YYYY-MM-DD');
                const status = $('#reportStatus').val();
                const paymentStatus = $('#reportPaymentStatus').val();
                const paymentMethod = $('#reportPaymentMethod').val();

                // Show loading state
                $(this).prop('disabled', true).html('<i class="bi bi-hourglass-split me-2"></i>جاري الإنشاء...');

                // Generate test report
                generateTestReport({
                    from_date: startDate,
                    to_date: endDate,
                    status: status,
                    payment_status: paymentStatus,
                    payment_method: paymentMethod
                });

                // Reset button state
                setTimeout(() => {
                    $(this).prop('disabled', false).html('<i class="bi bi-file-earmark-pdf me-2"></i>إنشاء تقرير تجريبي');
                }, 1000);
            });
        });

        function generateTestReport(filters) {
            // Sample customer data
            const customer = {
                name: 'أحمد محمد العميل',
                email: '<EMAIL>',
                phone: '+966501234567',
                created_at: '2023-01-15'
            };

            // Sample tasks data
            const tasks = [
                {
                    id: 1001,
                    created_at: '2024-08-25',
                    pickup: { address: 'الرياض - حي النخيل - شارع الملك فهد' },
                    delivery: { address: 'جدة - حي الصفا - شارع التحلية' },
                    driver: { name: 'محمد أحمد السائق' },
                    total_price: 850.00,
                    status: 'completed',
                    payment_status: 'completed',
                    payment_method: 'cash'
                },
                {
                    id: 1002,
                    created_at: '2024-08-26',
                    pickup: { address: 'الدمام - حي الفيصلية - شارع الأمير محمد' },
                    delivery: { address: 'الخبر - حي العليا - شارع الملك عبدالعزيز' },
                    driver: { name: 'علي حسن السائق' },
                    total_price: 450.00,
                    status: 'in_progress',
                    payment_status: 'pending',
                    payment_method: 'credit'
                },
                {
                    id: 1003,
                    created_at: '2024-08-27',
                    pickup: { address: 'مكة المكرمة - حي العزيزية - شارع إبراهيم الخليل' },
                    delivery: { address: 'الطائف - حي الشفا - شارع الملك فيصل' },
                    driver: null,
                    total_price: 650.00,
                    status: 'advertised',
                    payment_status: 'waiting',
                    payment_method: 'banking'
                }
            ];

            // Sample statistics
            const statistics = {
                total_tasks: 3,
                completed_tasks: 1,
                active_tasks: 1,
                canceled_tasks: 0,
                total_amount: 1950.00,
                paid_amount: 850.00,
                pending_amount: 1100.00
            };

            // Generate and open report
            const reportHTML = generateReportHTML(customer, tasks, statistics, filters);
            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportHTML);
            printWindow.document.close();
            printWindow.focus();
        }

        function generateReportHTML(customer, tasks, statistics, filters) {
            const fromDate = moment(filters.from_date).format('DD/MM/YYYY');
            const toDate = moment(filters.to_date).format('DD/MM/YYYY');
            const reportDate = moment().format('DD/MM/YYYY HH:mm');

            const statusLabels = {
                'advertised': 'معلن',
                'in_progress': 'قيد التنفيذ',
                'assign': 'مُعين',
                'started': 'بدأت',
                'completed': 'مكتملة',
                'canceled': 'ملغية'
            };

            const paymentStatusLabels = {
                'waiting': 'في الانتظار',
                'pending': 'معلق',
                'completed': 'مكتمل'
            };

            const paymentMethodLabels = {
                'cash': 'نقدي',
                'credit': 'بطاقة ائتمان',
                'banking': 'تحويل بنكي',
                'wallet': 'محفظة'
            };

            return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير المهام - ${customer.name}</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', sans-serif; margin: 0; padding: 20px; direction: rtl; }
        .report-container { max-width: 210mm; margin: 0 auto; background: white; }
        .report-header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #007bff; padding-bottom: 20px; }
        .company-name { font-size: 28px; font-weight: 800; color: #007bff; margin-bottom: 8px; }
        .report-title { font-size: 24px; font-weight: 700; color: #000; margin: 15px 0; }
        .report-info { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 12px; color: #666; }
        .customer-info { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 20px; }
        .customer-info h3 { font-size: 16px; font-weight: 700; color: #007bff; margin-bottom: 10px; border-bottom: 1px solid #007bff; padding-bottom: 5px; }
        .info-row { display: flex; margin-bottom: 8px; }
        .info-label { font-weight: 600; min-width: 120px; color: #495057; }
        .info-value { color: #212529; }
        .statistics-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; text-align: center; }
        .stat-number { font-size: 20px; font-weight: 700; color: #007bff; margin-bottom: 5px; }
        .stat-label { font-size: 12px; color: #6c757d; font-weight: 500; }
        .tasks-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 12px; }
        .tasks-table th { background-color: #007bff; color: white; padding: 10px 8px; text-align: center; font-weight: 600; border: 1px solid #0056b3; }
        .tasks-table td { padding: 8px; border: 1px solid #dee2e6; text-align: center; vertical-align: middle; }
        .tasks-table tbody tr:nth-child(even) { background-color: #f8f9fa; }
        .status-badge { padding: 3px 8px; border-radius: 12px; font-size: 10px; font-weight: 600; color: white; }
        .status-completed { background-color: #28a745; }
        .status-in_progress { background-color: #ffc107; color: #000; }
        .status-advertised { background-color: #17a2b8; }
        .payment-completed { background-color: #28a745; }
        .payment-pending { background-color: #ffc107; color: #000; }
        .payment-waiting { background-color: #6c757d; }
        .report-footer { margin-top: 30px; text-align: center; font-size: 11px; color: #6c757d; border-top: 1px solid #dee2e6; padding-top: 15px; }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <div class="company-name">شركة النقل الآمن</div>
            <div class="report-title">تقرير المهام التفصيلي</div>
            <div class="report-info">
                <span>تاريخ التقرير: ${reportDate}</span>
                <span>الفترة: من ${fromDate} إلى ${toDate}</span>
            </div>
        </div>
        
        <div class="customer-info">
            <h3>معلومات العميل</h3>
            <div class="info-row">
                <span class="info-label">اسم العميل:</span>
                <span class="info-value">${customer.name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">${customer.email}</span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value">${customer.phone}</span>
            </div>
        </div>
        
        <div class="statistics-section">
            <h3 style="font-size: 18px; font-weight: 700; color: #007bff; margin-bottom: 15px;">الإحصائيات العامة</h3>
            <div class="statistics-grid">
                <div class="stat-card">
                    <div class="stat-number">${statistics.total_tasks}</div>
                    <div class="stat-label">إجمالي المهام</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${statistics.completed_tasks}</div>
                    <div class="stat-label">المهام المكتملة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${statistics.active_tasks}</div>
                    <div class="stat-label">المهام النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${statistics.total_amount.toFixed(2)} ريال</div>
                    <div class="stat-label">إجمالي المبلغ</div>
                </div>
            </div>
        </div>
        
        <div class="tasks-section">
            <h3 style="font-size: 18px; font-weight: 700; color: #007bff; margin-bottom: 15px;">تفاصيل المهام</h3>
            <table class="tasks-table">
                <thead>
                    <tr>
                        <th>رقم المهمة</th>
                        <th>التاريخ</th>
                        <th>من</th>
                        <th>إلى</th>
                        <th>السائق</th>
                        <th>المبلغ</th>
                        <th>حالة المهمة</th>
                        <th>حالة الدفع</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    ${tasks.map(task => `
                    <tr>
                        <td>#${task.id}</td>
                        <td>${moment(task.created_at).format('DD/MM/YYYY')}</td>
                        <td>${task.pickup.address.substring(0, 25)}...</td>
                        <td>${task.delivery.address.substring(0, 25)}...</td>
                        <td>${task.driver ? task.driver.name : 'غير مُعين'}</td>
                        <td>${task.total_price.toFixed(2)} ريال</td>
                        <td><span class="status-badge status-${task.status}">${statusLabels[task.status]}</span></td>
                        <td><span class="status-badge payment-${task.payment_status}">${paymentStatusLabels[task.payment_status]}</span></td>
                        <td>${paymentMethodLabels[task.payment_method]}</td>
                    </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        
        <div class="report-footer">
            <p><strong>شركة النقل الآمن</strong> | هاتف: +966-XX-XXX-XXXX | البريد الإلكتروني: <EMAIL></p>
            <p>تم إنشاء هذا التقرير تلقائياً في ${reportDate}</p>
        </div>
    </div>

    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
        
        window.addEventListener('afterprint', function() {
            window.close();
        });
    </script>
</body>
</html>
            `;
        }
    </script>
</body>
</html>

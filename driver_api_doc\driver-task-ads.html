<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Task Ads API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-put {
        background: #ffc107;
        color: #212529;
      }

      .method-delete {
        background: #dc3545;
        color: white;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      .auth-required {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .auth-icon {
        color: #856404;
        margin-left: 10px;
      }

      .warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .warning-icon {
        color: #856404;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Task Ads API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-bullhorn"></i> Driver Task Ads API</h1>
        <p>عرض إعلانات المهام المتاحة وتقديم العروض والمزايدات</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <div class="auth-required">
        <i class="fas fa-lock auth-icon"></i>
        <strong>مصادقة مطلوبة:</strong> جميع endpoints في هذا القسم تتطلب Bearer Token في header للمصادقة.
      </div>

      <div class="note">
        <i class="fas fa-info-circle note-icon"></i>
        <strong>نظام إعلانات المهام:</strong>
        يسمح هذا النظام للعملاء بنشر إعلانات للمهام والسائقين بتقديم عروض تنافسية. يتم اختيار أفضل عرض من قبل العميل.
      </div>

      <!-- Get Stats Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">إحصائيات إعلانات المهام</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/task-ads/stats</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على إحصائيات إعلانات المهام للسائق مثل عدد الإعلانات المتاحة، العروض المقدمة، والعروض المقبولة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Statistics retrieved successfully",
    "data": {
        "available_ads": 15,
        "my_offers": 8,
        "accepted_offers": 3
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-chart-bar"></i> شرح الإحصائيات</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>الإحصائية</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>available_ads</code></td>
                <td>عدد الإعلانات المتاحة التي يمكن للسائق تقديم عروض عليها</td>
              </tr>
              <tr>
                <td><code>my_offers</code></td>
                <td>عدد العروض المقدمة من السائق والمعلقة</td>
              </tr>
              <tr>
                <td><code>accepted_offers</code></td>
                <td>عدد العروض المقبولة من السائق</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Get Available Ads Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">الحصول على الإعلانات المتاحة</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/task-ads</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على قائمة الإعلانات المتاحة للسائق حسب حجم مركبته مع إمكانية التصفية والترقيم.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات الاستعلام</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رقم الصفحة (افتراضي: 1)</td>
              </tr>
              <tr>
                <td><code>per_page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>عدد العناصر في الصفحة (1-50، افتراضي: 10)</td>
              </tr>
              <tr>
                <td><code>status</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>تصفية حسب الحالة (running, closed, all)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Task ads retrieved successfully",
    "data": {
        "ads": [
            {
                "id": 45,
                "status": "running",
                "created_at": "2024-01-15T10:30:00.000000Z",
                "expires_at": "2024-01-16T10:30:00.000000Z",
                "offers_count": 5,
                "max_price": 200.00,
                "min_price": 150.00,
                "my_offer": null,
                "task": {
                    "id": 123,
                    "description": "نقل أثاث منزلي من الرياض إلى جدة",
                    "pickup_address": "الرياض، حي النرجس",
                    "delivery_address": "جدة، حي الروضة",
                    "estimated_price": 180.00,
                    "vehicle_size": {
                        "id": 2,
                        "name": "3 طن",
                        "description": "شاحنة متوسطة"
                    },
                    "customer": {
                        "id": 67,
                        "name": "محمد أحمد",
                        "rating": 4.5
                    }
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 3,
            "per_page": 10,
            "total": 25,
            "from": 1,
            "to": 10
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Submit Offer Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تقديم عرض على إعلان</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/task-ads/{adId}/offers</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يقدم السائق عرضاً على إعلان مهمة محدد بسعر ووصف.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>adId</code></td>
                <td>integer</td>
                <td>معرف إعلان المهمة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>price</code></td>
                <td>numeric</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>السعر المقترح للمهمة (يجب أن يكون أكبر من 0)</td>
              </tr>
              <tr>
                <td><code>description</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>وصف العرض أو ملاحظات إضافية (حد أقصى 400 حرف)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "price": 175.50,
    "description": "يمكنني تنفيذ المهمة خلال 24 ساعة مع ضمان الجودة والسرعة"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-201">201 Created</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Offer submitted successfully",
    "data": {
        "offer_id": 89,
        "price": 175.50,
        "description": "يمكنني تنفيذ المهمة خلال 24 ساعة مع ضمان الجودة والسرعة",
        "created_at": "2024-01-15T14:30:00.000000Z"
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-exclamation-triangle"></i> أخطاء محتملة</h3>

          <div class="response-example">
            <span class="status-code status-400">400 Bad Request</span>
            <strong>الإعلان لا يقبل عروض جديدة:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "This advertisement is no longer accepting offers"
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-400">400 Bad Request</span>
            <strong>عرض موجود مسبقاً:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "You have already submitted an offer for this advertisement"
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-422">422 Unprocessable Entity</span>
            <strong>خطأ في التحقق من البيانات:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "price": ["The price field is required."],
        "description": ["The description may not be greater than 400 characters."]
    }
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Get My Offers Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">عروضي المقدمة</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/my-offers</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على قائمة العروض التي قدمها السائق على إعلانات المهام المختلفة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات الاستعلام</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رقم الصفحة (افتراضي: 1)</td>
              </tr>
              <tr>
                <td><code>per_page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>عدد العناصر في الصفحة (1-50، افتراضي: 10)</td>
              </tr>
              <tr>
                <td><code>status</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>تصفية حسب حالة العرض (pending, accepted, rejected)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "My offers retrieved successfully",
    "data": {
        "offers": [
            {
                "id": 89,
                "price": 175.50,
                "description": "يمكنني تنفيذ المهمة خلال 24 ساعة",
                "accepted": false,
                "created_at": "2024-01-15T14:30:00.000000Z",
                "ad": {
                    "id": 45,
                    "status": "running",
                    "expires_at": "2024-01-16T10:30:00.000000Z",
                    "offers_count": 6,
                    "task": {
                        "id": 123,
                        "description": "نقل أثاث منزلي",
                        "pickup_address": "الرياض، حي النرجس",
                        "delivery_address": "جدة، حي الروضة",
                        "customer": {
                            "name": "محمد أحمد",
                            "rating": 4.5
                        }
                    }
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 2,
            "per_page": 10,
            "total": 12,
            "from": 1,
            "to": 10
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Get Ad Details Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">تفاصيل إعلان المهمة</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/task-ads/{adId}</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على تفاصيل إعلان مهمة محدد مع جميع المعلومات المرتبطة به.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات المسار</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>adId</code></td>
                <td>integer</td>
                <td>معرف إعلان المهمة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Task ad details retrieved successfully",
    "data": {
        "ad": {
            "id": 45,
            "status": "running",
            "created_at": "2024-01-15T10:30:00.000000Z",
            "expires_at": "2024-01-16T10:30:00.000000Z",
            "offers_count": 6,
            "max_price": 200.00,
            "min_price": 150.00,
            "average_price": 175.00,
            "my_offer": {
                "id": 89,
                "price": 175.50,
                "description": "يمكنني تنفيذ المهمة خلال 24 ساعة",
                "created_at": "2024-01-15T14:30:00.000000Z"
            },
            "task": {
                "id": 123,
                "description": "نقل أثاث منزلي من الرياض إلى جدة",
                "special_instructions": "التعامل بحذر مع الأثاث القديم",
                "estimated_duration": 480,
                "pickup_time": "2024-01-16T08:00:00.000000Z",
                "pickup_point": {
                    "address": "الرياض، حي النرجس، شارع الأمير محمد",
                    "latitude": 24.7136,
                    "longitude": 46.6753,
                    "contact_name": "محمد أحمد",
                    "contact_phone": "966501234567"
                },
                "delivery_point": {
                    "address": "جدة، حي الروضة، شارع الملك عبدالعزيز",
                    "latitude": 21.5433,
                    "longitude": 39.1728,
                    "contact_name": "سارة محمد",
                    "contact_phone": "966501234568"
                },
                "vehicle_size": {
                    "id": 2,
                    "name": "3 طن",
                    "description": "شاحنة متوسطة الحجم"
                },
                "customer": {
                    "id": 67,
                    "name": "محمد أحمد",
                    "rating": 4.5,
                    "completed_tasks": 23
                }
            }
        }
    }
}</code></pre>
        </div>
      </div>

      <div class="warning">
        <i class="fas fa-exclamation-triangle warning-icon"></i>
        <strong>ملاحظة مهمة:</strong> يمكن للسائق تقديم عرض واحد فقط لكل إعلان. في حالة الرغبة في تعديل العرض، يجب حذف
        العرض الحالي أولاً ثم تقديم عرض جديد.
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

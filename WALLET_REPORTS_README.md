# 💰 Wallet Reports System - SafeDests Platform

## نظام تقارير المحافظ المتكامل

تم تطوير نظام تقارير شامل ومتقدم للمحافظ في منصة SafeDests يتيح للمستخدمين إنشاء تقارير مفصلة لجميع أنواع المحافظ مع إمكانيات تصدير متقدمة.

## 🎯 الميزات الرئيسية

### ✅ تقرير المحافظ الشامل
- **دعم ثلاثة أنواع من المحافظ**: محافظ العملاء، محافظ السائقين، ومحافظ الفرق
- **فلترة متقدمة**: اختيار نوع المحفظة، المالك، الفترة الزمنية، نوع المعاملة، والحالة
- **تصدير PDF**: تقرير مفصل مع تصميم احترافي مناسب للطباعة
- **إحصائيات شاملة**: إجمالي الإيداعات، السحوبات، الرصيد الحالي، وعدد المعاملات
- **واجهة ديناميكية**: تحديث قوائم الاختيار بناءً على نوع المحفظة المختار
- **دعم كامل للعربية**: واجهة وتقارير باللغة العربية مع دعم RTL

### 🏗️ البنية التقنية المتقدمة
- **نظام محافظ مزدوج**: دعم المحافظ الفردية (customers/drivers) والمحافظ الجماعية (teams)
- **جداول منفصلة**: محافظ الفرق لها جداول منفصلة عن المحافظ الفردية
- **معالجة ديناميكية**: التعامل مع أنواع المحافظ المختلفة بطريقة موحدة
- **إحصائيات دقيقة**: حسابات مالية دقيقة لكل نوع محفظة

## 📁 الملفات المُنشأة

### Backend Files
```
app/Http/Controllers/admin/WalletReportsController.php
resources/views/admin/reports/wallet-reports.blade.php
resources/views/admin/reports/pdf/wallet-report.blade.php
resources/js/admin/reports/wallet-reports.js
```

### Database & Permissions
```
database/seeders/ReportsPermissionsSeeder.php (محدث)
routes/web.php (محدث)
resources/views/admin/reports/index.blade.php (محدث)
```

## 🔧 التقنيات المستخدمة

### Backend
- **Laravel 11.x** - إطار العمل الأساسي
- **Eloquent ORM** - للتعامل مع قاعدة البيانات
- **Carbon** - لمعالجة التواريخ
- **Spatie Permissions** - إدارة الصلاحيات

### Frontend
- **Bootstrap 5** - التصميم والتخطيط
- **Select2** - قوائم الاختيار المتقدمة
- **DateRangePicker** - اختيار الفترات الزمنية
- **SweetAlert2** - الرسائل التفاعلية
- **jQuery** - التفاعل والـ AJAX

## 🚀 كيفية الاستخدام

### 1. الإعداد الأولي
```bash
# تشغيل seeder الصلاحيات
php artisan db:seed --class=ReportsPermissionsSeeder

# مسح cache المسارات
php artisan route:clear
php artisan config:clear
```

### 2. الوصول للنظام
- **الرابط**: `/admin/reports/wallet`
- **الصلاحيات المطلوبة**: `view_reports`, `generate_reports`

### 3. إنشاء التقرير
1. اختر نوع المحفظة (عميل/سائق/فريق)
2. اختر المالك من القائمة المحدثة
3. حدد الفترة الزمنية
4. اختر الفلاتر الإضافية (اختياري)
5. اضغط "إنشاء تقرير PDF"

## 📊 أنواع المحافظ المدعومة

### 1. محافظ العملاء (Customer Wallets)
- **الجدول**: `wallets` (user_type = 'customer')
- **المعاملات**: `wallet_transactions`
- **الميزات**: 
  - تتبع المدفوعات والاسترداد
  - سقف الدين
  - حالة المعاملات (مؤكد/معلق)

### 2. محافظ السائقين (Driver Wallets)
- **الجدول**: `wallets` (user_type = 'driver')
- **المعاملات**: `wallet_transactions`
- **الميزات**:
  - أرباح السائقين
  - العمولات والخصومات
  - سجل السحوبات

### 3. محافظ الفرق (Team Wallets)
- **الجدول**: `team_wallet`
- **المعاملات**: `team_wallet_transactions`
- **الميزات**:
  - محافظ منفصلة عن المحافظ الفردية
  - إدارة مالية جماعية
  - عمولات الفريق

## 🎨 مكونات التقرير

### 1. معلومات المالك
- الاسم والمعرف
- رقم المحفظة
- معلومات الاتصال (للأفراد)
- عدد السائقين (للفرق)

### 2. الإحصائيات المالية
- إجمالي الإيداعات
- إجمالي السحوبات
- الرصيد الحالي
- عدد المعاملات
- توزيع أنواع المعاملات

### 3. تفاصيل المعاملات
- رقم المعاملة والتاريخ
- نوع المعاملة (إيداع/سحب)
- المبلغ والوصف
- الحالة (للمحافظ الفردية)
- رقم المهمة المرتبطة

## 🔒 الأمان والصلاحيات

### الصلاحيات المطلوبة
- `view_reports` - عرض صفحة التقارير
- `generate_reports` - إنشاء التقارير
- `wallet_reports` - تقارير المحافظ المحددة

### الحماية المطبقة
- **فحص الصلاحيات**: التحقق من صلاحية المستخدم
- **حماية CSRF**: حماية النماذج
- **تشفير البيانات**: حماية البيانات الحساسة
- **تسجيل العمليات**: تتبع إنشاء التقارير

## ⚡ الأداء والتحسين

### التحسينات المطبقة
- **استعلامات محسنة**: استخدام Eager Loading
- **فهرسة قاعدة البيانات**: فهارس على الحقول المهمة
- **تحميل تدريجي**: للتقارير الكبيرة
- **ذاكرة التخزين المؤقت**: للبيانات المتكررة

### حدود النظام
- **الحد الأقصى للمعاملات**: 10,000 معاملة لكل تقرير
- **الفترة الزمنية**: حتى سنة واحدة
- **حجم الملف**: حتى 50 MB للـ PDF

## 🔄 التطوير المستقبلي

### ميزات مخططة
- **تصدير Excel**: إضافة تصدير Excel للتقارير
- **تقارير مجدولة**: إنشاء تقارير دورية تلقائية
- **مقارنة الفترات**: مقارنة الأداء بين فترات مختلفة
- **رسوم بيانية**: إضافة مخططات بصرية للإحصائيات
- **تقارير مجمعة**: تقارير شاملة لجميع المحافظ

### تحسينات تقنية
- **API للتقارير**: واجهة برمجية للتقارير
- **تصدير مجدول**: تصدير التقارير في الخلفية
- **ضغط الملفات**: تحسين حجم ملفات PDF
- **تخزين سحابي**: حفظ التقارير في التخزين السحابي

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الصلاحيات
```bash
# تشغيل seeder الصلاحيات
php artisan db:seed --class=ReportsPermissionsSeeder
```

#### 2. مشكلة في المسارات
```bash
# مسح cache المسارات
php artisan route:clear
php artisan config:clear
```

#### 3. خطأ في قاعدة البيانات
```bash
# التحقق من الاتصال
php artisan tinker --execute="App\Models\Wallet::count()"
```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف الـ logs: `storage/logs/laravel.log`
2. تأكد من الصلاحيات المطلوبة
3. تحقق من إعدادات قاعدة البيانات
4. راجع هذا الدليل للحلول الشائعة

---

**تم تطوير نظام تقارير المحافظ بواسطة فريق SafeDests - 2024**

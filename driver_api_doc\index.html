<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeDests Driver API Documentation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .controllers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .controller-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .controller-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .controller-card h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .controller-card .icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .controller-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .methods-count {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            color: #495057;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .overview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            color: white;
            padding: 20px 0;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controllers-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-truck"></i> SafeDests Driver API</h1>
            <p>دليل شامل لجميع APIs الخاصة بتطبيق السائقين</p>
        </div>

        <div class="overview">
            <h2><i class="fas fa-info-circle"></i> نظرة عامة</h2>
            <p>
                يحتوي هذا الدليل على توثيق شامل لجميع APIs الخاصة بتطبيق السائقين في منصة SafeDests. 
                تم تنظيم الـ APIs في مجموعات منطقية حسب الوظيفة، مع شرح مفصل لكل endpoint وطريقة استخدامه.
            </p>
            
            <div class="stats">
                <div class="stat-card">
                    <h3>8</h3>
                    <p>Controllers</p>
                </div>
                <div class="stat-card">
                    <h3>45+</h3>
                    <p>API Endpoints</p>
                </div>
                <div class="stat-card">
                    <h3>100%</h3>
                    <p>موثق</p>
                </div>
            </div>
        </div>

        <div class="controllers-grid">
            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-sign-in-alt"></i></div>
                    Driver Authentication
                </h2>
                <p>إدارة تسجيل الدخول والخروج، إعادة تعيين كلمة المرور، وإدارة الجلسات للسائقين</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 4 Methods
                </div>
                <a href="driver-auth.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-user-plus"></i></div>
                    Driver Registration
                </h2>
                <p>تسجيل السائقين الجدد، الحصول على بيانات التسجيل، وإدارة الحقول الإضافية</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 4 Methods
                </div>
                <a href="driver-registration.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-user"></i></div>
                    Driver Profile
                </h2>
                <p>إدارة ملف السائق الشخصي، تحديث البيانات، تغيير كلمة المرور، والإحصائيات</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 4 Methods
                </div>
                <a href="driver-profile.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-tasks"></i></div>
                    Driver Tasks
                </h2>
                <p>إدارة المهام المخصصة للسائق، قبول المهام، تحديث الحالة، والتتبع</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 8 Methods
                </div>
                <a href="driver-tasks.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-bullhorn"></i></div>
                    Task Ads
                </h2>
                <p>عرض إعلانات المهام المتاحة، تقديم العروض، وإدارة المزايدات</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 6 Methods
                </div>
                <a href="driver-task-ads.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-wallet"></i></div>
                    Driver Wallet
                </h2>
                <p>إدارة محفظة السائق، عرض الرصيد، تاريخ المعاملات، والسحب</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 4 Methods
                </div>
                <a href="driver-wallet.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-map-marker-alt"></i></div>
                    Location & Status
                </h2>
                <p>تحديث موقع السائق، إدارة الحالة (متاح/مشغول)، وتحديث FCM Token</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 4 Methods
                </div>
                <a href="driver-location.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>

            <div class="controller-card">
                <h2>
                    <div class="icon"><i class="fas fa-bell"></i></div>
                    Notifications
                </h2>
                <p>إدارة الإشعارات، عرض الرسائل، تحديث إعدادات الإشعارات</p>
                <div class="methods-count">
                    <i class="fas fa-code"></i> 6 Methods
                </div>
                <a href="driver-notifications.html" class="btn">
                    <i class="fas fa-book"></i> عرض التوثيق
                </a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 SafeDests - Driver API Documentation</p>
            <p>تم إنشاء هذا التوثيق بواسطة Augment Agent</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>

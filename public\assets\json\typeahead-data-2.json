[{"year": "1961", "value": "West Side Story", "tokens": ["West", "Side", "Story"]}, {"year": "1962", "value": "Lawrence of Arabia", "tokens": ["<PERSON>", "of", "Arabia"]}, {"year": "1963", "value": "<PERSON>", "tokens": ["<PERSON>", "<PERSON>"]}, {"year": "1964", "value": "My <PERSON> Lady", "tokens": ["My", "Fair", "Lady"]}, {"year": "1965", "value": "The Sound of Music", "tokens": ["The", "Sound", "of", "Music"]}, {"year": "1966", "value": "A Man for All Seasons", "tokens": ["A", "Man", "for", "All", "Seasons"]}, {"year": "1967", "value": "In the Heat of the Night", "tokens": ["In", "the", "Heat", "of", "the", "Night"]}, {"year": "1968", "value": "Oliver!", "tokens": ["Oliver!"]}, {"year": "1969", "value": "Midnight Cowboy", "tokens": ["Midnight", "Cowboy"]}, {"year": "1970", "value": "<PERSON>", "tokens": ["<PERSON>"]}, {"year": "1971", "value": "The French Connection", "tokens": ["The", "French", "Connection"]}, {"year": "1972", "value": "The Godfather", "tokens": ["The", "Godfather"]}, {"year": "1973", "value": "The Sting", "tokens": ["The", "Sting"]}, {"year": "1974", "value": "The Godfather Part II", "tokens": ["The", "Godfather", "Part", "II"]}, {"year": "1975", "value": "One Flew over the Cuckoo's Nest", "tokens": ["One", "Flew", "over", "the", "<PERSON><PERSON><PERSON>'s", "Nest"]}, {"year": "1976", "value": "<PERSON>", "tokens": ["<PERSON>"]}, {"year": "1977", "value": "<PERSON>", "tokens": ["<PERSON>", "Hall"]}, {"year": "1978", "value": "The Deer Hunter", "tokens": ["The", "Deer", "<PERSON>"]}, {"year": "1979", "value": "<PERSON> vs. <PERSON>", "tokens": ["<PERSON>", "vs.", "<PERSON>"]}, {"year": "1980", "value": "Ordinary People", "tokens": ["Ordinary", "People"]}, {"year": "1981", "value": "Chariots of Fire", "tokens": ["Chariots", "of", "Fire"]}, {"year": "1982", "value": "<PERSON>", "tokens": ["<PERSON>"]}, {"year": "1983", "value": "Terms of Endearment", "tokens": ["Terms", "of", "Endearment"]}, {"year": "1984", "value": "<PERSON><PERSON><PERSON>", "tokens": ["<PERSON><PERSON><PERSON>"]}, {"year": "1985", "value": "Out of Africa", "tokens": ["Out", "of", "Africa"]}, {"year": "1986", "value": "Platoon", "tokens": ["Platoon"]}, {"year": "1987", "value": "The Last Emperor", "tokens": ["The", "Last", "Emperor"]}, {"year": "1988", "value": "<PERSON>", "tokens": ["Rain", "Man"]}, {"year": "1989", "value": "Driving Miss Daisy", "tokens": ["Driving", "Miss", "<PERSON>"]}, {"year": "1990", "value": "Dances With Wolves", "tokens": ["Dances", "With", "Wolves"]}, {"year": "1991", "value": "The Silence of the Lambs", "tokens": ["The", "Silence", "of", "the", "<PERSON><PERSON>"]}, {"year": "1992", "value": "Unforgiven", "tokens": ["Unforgiven"]}, {"year": "1993", "value": "<PERSON><PERSON><PERSON>’s List", "tokens": ["<PERSON><PERSON><PERSON>’s", "List"]}, {"year": "1994", "value": "<PERSON>", "tokens": ["<PERSON>", "Gump"]}, {"year": "1995", "value": "Braveheart", "tokens": ["Braveheart"]}, {"year": "1996", "value": "The English Patient", "tokens": ["The", "English", "Patient"]}, {"year": "1997", "value": "Titanic", "tokens": ["Titanic"]}, {"year": "1998", "value": "Shakespeare in Love", "tokens": ["Shakespeare", "in", "Love"]}, {"year": "1999", "value": "American Beauty", "tokens": ["American", "Beauty"]}, {"year": "2000", "value": "Gladiator", "tokens": ["Gladiator"]}, {"year": "2001", "value": "A Beautiful Mind", "tokens": ["A", "Beautiful", "Mind"]}, {"year": "2002", "value": "Chicago", "tokens": ["Chicago"]}, {"year": "2003", "value": "The Lord of the Rings: The Return of the King", "tokens": ["The", "Lord", "of", "the", "Rings:", "The", "Return", "of", "the", "King"]}, {"year": "2004", "value": "Million Dollar Baby", "tokens": ["Million", "Dollar", "Baby"]}, {"year": "2005", "value": "Crash", "tokens": ["Crash"]}, {"year": "2006", "value": "The Departed", "tokens": ["The", "Departed"]}, {"year": "2007", "value": "No Country for Old Men", "tokens": ["No", "Country", "for", "Old", "Men"]}, {"year": "2008", "value": "Slumdog Millionaire", "tokens": ["Slumdog", "Millionaire"]}, {"year": "2009", "value": "The Hurt Locker", "tokens": ["The", "Hurt", "Locker"]}, {"year": "2010", "value": "The King's Speech", "tokens": ["The", "King's", "Speech"]}, {"year": "2011", "value": "The Artist", "tokens": ["The", "Artist"]}, {"year": "2012", "value": "Argo", "tokens": ["Argo"]}]
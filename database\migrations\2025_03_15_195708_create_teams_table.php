<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('teams', function (Blueprint $table) {
      $table->id()->startingValue(10000);
      $table->string('name')->unique();
      $table->text('address')->nullable();
      $table->text('note')->nullable();
      $table->enum('team_commission_type', ['rate', 'fixed', 'subscription'])->nullable();
      $table->decimal('team_commission_value', 10, 2)->nullable();
      $table->integer('location_update_interval')->default(30)->nullable();
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('teams');
  }
};

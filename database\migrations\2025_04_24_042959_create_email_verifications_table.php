<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('email_verifications', function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger('verifiable_id');
      $table->string('verifiable_type');
      $table->string('token');
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('email_verifications');
  }
};

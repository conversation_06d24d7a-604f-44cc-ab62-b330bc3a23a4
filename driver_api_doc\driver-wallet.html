<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Wallet API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-put {
        background: #ffc107;
        color: #212529;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      .auth-required {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .auth-icon {
        color: #856404;
        margin-left: 10px;
      }

      .wallet-feature {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .wallet-icon {
        color: #155724;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Wallet API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-wallet"></i> Driver Wallet API</h1>
        <p>إدارة المحفظة المالية للسائق والمعاملات والأرباح</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <div class="auth-required">
        <i class="fas fa-lock auth-icon"></i>
        <strong>مصادقة مطلوبة:</strong> جميع endpoints في هذا القسم تتطلب Bearer Token في header للمصادقة.
      </div>

      <div class="wallet-feature">
        <i class="fas fa-coins wallet-icon"></i>
        <strong>نظام المحفظة المالية:</strong>
        يوفر هذا النظام إدارة شاملة للأموال والأرباح مع تتبع دقيق للمعاملات وإحصائيات مفصلة للأرباح.
      </div>

      <!-- Get Wallet Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">معلومات المحفظة</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/wallet</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على معلومات المحفظة المالية للسائق بما في ذلك الرصيد الحالي، الأرباح الإجمالية، والمبالغ المعلقة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Wallet information retrieved successfully",
    "data": {
        "wallet": {
            "balance": 1250.75,
            "debt_ceiling": 0.00,
            "pending_amount": 320.50,
            "total_earnings": 5680.25,
            "currency": "SAR"
        },
        "commission": {
            "type": "percentage",
            "value": 15.0
        }
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-chart-bar"></i> شرح البيانات المالية</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>الحقل</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>balance</code></td>
                <td>الرصيد المتاح للسحب حالياً</td>
              </tr>
              <tr>
                <td><code>debt_ceiling</code></td>
                <td>حد الدين المسموح (عادة 0 للسائقين)</td>
              </tr>
              <tr>
                <td><code>pending_amount</code></td>
                <td>المبالغ المعلقة من المهام المكتملة غير المدفوعة</td>
              </tr>
              <tr>
                <td><code>total_earnings</code></td>
                <td>إجمالي الأرباح المحققة من جميع المهام</td>
              </tr>
              <tr>
                <td><code>commission.type</code></td>
                <td>نوع العمولة (percentage أو fixed)</td>
              </tr>
              <tr>
                <td><code>commission.value</code></td>
                <td>قيمة العمولة (نسبة مئوية أو مبلغ ثابت)</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Get Transactions Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">سجل المعاملات المالية</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/wallet/transactions</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على سجل المعاملات المالية للسائق مع إمكانية التصفية حسب النوع والتاريخ.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات الاستعلام</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رقم الصفحة (افتراضي: 1)</td>
              </tr>
              <tr>
                <td><code>per_page</code></td>
                <td>integer</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>عدد العناصر في الصفحة (1-50، افتراضي: 10)</td>
              </tr>
              <tr>
                <td><code>type</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>نوع المعاملة (credit, debit, commission, withdrawal, deposit)</td>
              </tr>
              <tr>
                <td><code>from</code></td>
                <td>date</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>تاريخ البداية (YYYY-MM-DD)</td>
              </tr>
              <tr>
                <td><code>to</code></td>
                <td>date</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>تاريخ النهاية (YYYY-MM-DD)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Transactions retrieved successfully",
    "data": {
        "transactions": [
            {
                "id": 156,
                "amount": 180.50,
                "type": "credit",
                "description": "أرباح من المهمة #123 - نقل أثاث منزلي",
                "status": "completed",
                "task_id": 123,
                "created_at": "2024-01-15T14:30:00.000000Z",
                "maturity_time": "2024-01-15T14:30:00.000000Z",
                "sequence": "TXN-2024-001156"
            },
            {
                "id": 155,
                "amount": -50.00,
                "type": "withdrawal",
                "description": "سحب نقدي - طلب رقم #45",
                "status": "pending",
                "task_id": null,
                "created_at": "2024-01-14T10:15:00.000000Z",
                "maturity_time": "2024-01-16T10:15:00.000000Z",
                "sequence": "TXN-2024-001155"
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 8,
            "per_page": 10,
            "total": 78,
            "from": 1,
            "to": 10
        }
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-list"></i> أنواع المعاملات</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>النوع</th>
                <th>الوصف</th>
                <th>الإشارة</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>credit</code></td>
                <td>إيداع أرباح من المهام المكتملة</td>
                <td>موجب (+)</td>
              </tr>
              <tr>
                <td><code>debit</code></td>
                <td>خصم أو سحب من المحفظة</td>
                <td>سالب (-)</td>
              </tr>
              <tr>
                <td><code>commission</code></td>
                <td>عمولة الشركة المخصومة</td>
                <td>سالب (-)</td>
              </tr>
              <tr>
                <td><code>withdrawal</code></td>
                <td>طلب سحب نقدي</td>
                <td>سالب (-)</td>
              </tr>
              <tr>
                <td><code>deposit</code></td>
                <td>إيداع إضافي من الإدارة</td>
                <td>موجب (+)</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Get Earnings Stats Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">إحصائيات الأرباح</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/wallet/earnings/stats</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على إحصائيات مفصلة للأرباح حسب فترات زمنية مختلفة مع مقارنات شاملة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> معاملات الاستعلام</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>period</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>الفترة الزمنية (today, week, month, year) - افتراضي: month</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Earnings statistics retrieved successfully",
    "data": {
        "period": "month",
        "stats": {
            "total_earnings": 2450.75,
            "total_tasks": 18,
            "average_earning_per_task": 136.15,
            "period_start": "2024-01-01",
            "period_end": "2024-01-31"
        },
        "all_time": {
            "total_earnings": 15680.25,
            "total_tasks": 127,
            "average_earning_per_task": 123.47
        }
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-calendar-alt"></i> الفترات الزمنية المتاحة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>القيمة</th>
                <th>الوصف</th>
                <th>النطاق الزمني</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>today</code></td>
                <td>اليوم الحالي</td>
                <td>من بداية اليوم حتى نهايته</td>
              </tr>
              <tr>
                <td><code>week</code></td>
                <td>الأسبوع الحالي</td>
                <td>من بداية الأسبوع حتى نهايته</td>
              </tr>
              <tr>
                <td><code>month</code></td>
                <td>الشهر الحالي</td>
                <td>من بداية الشهر حتى نهايته</td>
              </tr>
              <tr>
                <td><code>year</code></td>
                <td>السنة الحالية</td>
                <td>من بداية السنة حتى نهايتها</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="note">
        <i class="fas fa-info-circle note-icon"></i>
        <strong>ملاحظة مهمة:</strong>
        جميع المبالغ المالية معروضة بالريال السعودي (SAR). يتم تحديث الأرباح تلقائياً عند إكمال المهام وموافقة العميل
        على التسليم.
      </div>

      <div class="wallet-feature">
        <i class="fas fa-shield-alt wallet-icon"></i>
        <strong>الأمان المالي:</strong>
        جميع المعاملات المالية محمية ومشفرة. يتم تسجيل كل معاملة مع رقم تسلسلي فريد لضمان الشفافية والمتابعة.
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

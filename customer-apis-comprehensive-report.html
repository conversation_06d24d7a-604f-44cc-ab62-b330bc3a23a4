<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تقرير شامل - APIs تطبيق العملاء SafeDest</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        color: #2c3e50;
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header p {
        color: #7f8c8d;
        font-size: 1.2em;
      }

      .section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .section h2 {
        color: #2c3e50;
        font-size: 1.8em;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 3px solid #3498db;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .section h3 {
        color: #34495e;
        font-size: 1.4em;
        margin: 25px 0 15px 0;
        padding: 10px 15px;
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
      }

      .section h4 {
        color: #2980b9;
        font-size: 1.2em;
        margin: 20px 0 10px 0;
        padding: 8px 12px;
        background: #ecf0f1;
        border-radius: 6px;
        border-left: 4px solid #3498db;
      }

      .api-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .api-table th {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 15px;
        text-align: right;
        font-weight: 600;
      }

      .api-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #ecf0f1;
        vertical-align: top;
      }

      .api-table tr:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
        transition: all 0.3s ease;
      }

      .method {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.9em;
      }

      .method.get {
        background: #d4edda;
        color: #155724;
      }
      .method.post {
        background: #cce5ff;
        color: #004085;
      }
      .method.put {
        background: #fff3cd;
        color: #856404;
      }
      .method.delete {
        background: #f8d7da;
        color: #721c24;
      }

      .priority {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.9em;
      }

      .priority.high {
        background: #ff6b6b;
        color: white;
      }
      .priority.medium {
        background: #feca57;
        color: white;
      }
      .priority.low {
        background: #48dbfb;
        color: white;
      }

      .feature-list {
        list-style: none;
        padding: 0;
      }

      .feature-list li {
        padding: 10px 15px;
        margin: 8px 0;
        background: #f8f9fa;
        border-radius: 8px;
        border-right: 4px solid #3498db;
        transition: all 0.3s ease;
      }

      .feature-list li:hover {
        background: #e9ecef;
        transform: translateX(-5px);
      }

      .feature-list li::before {
        content: '✓';
        color: #27ae60;
        font-weight: bold;
        margin-left: 10px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .stat-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
      }

      .stat-card h3 {
        font-size: 2em;
        margin-bottom: 5px;
        background: none;
        color: white;
        padding: 0;
        box-shadow: none;
      }

      .stat-card p {
        font-size: 1.1em;
        opacity: 0.9;
      }

      .code-block {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin: 15px 0;
        overflow-x: auto;
        box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .timeline {
        position: relative;
        padding: 20px 0;
      }

      .timeline::before {
        content: '';
        position: absolute;
        right: 30px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #3498db;
      }

      .timeline-item {
        position: relative;
        margin: 20px 0;
        padding-right: 60px;
      }

      .timeline-item::before {
        content: '';
        position: absolute;
        right: 24px;
        top: 10px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #3498db;
      }

      .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-right: 4px solid #3498db;
      }

      .icon {
        font-size: 1.2em;
        margin-left: 8px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2em;
        }

        .section {
          padding: 20px;
        }

        .api-table {
          font-size: 0.9em;
        }

        .stats-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 تقرير شامل - APIs تطبيق العملاء SafeDest</h1>
        <p>تحليل مفصل وخطة تطوير شاملة لجميع واجهات برمجة التطبيقات المطلوبة</p>
      </div>

      <div class="section">
        <h2><span class="icon">📊</span>نظرة عامة على المشروع</h2>

        <div class="stats-grid">
          <div class="stat-card">
            <h3>45+</h3>
            <p>API Endpoint</p>
          </div>
          <div class="stat-card">
            <h3>8</h3>
            <p>وحدات رئيسية</p>
          </div>
          <div class="stat-card">
            <h3>15</h3>
            <p>نموذج بيانات</p>
          </div>
          <div class="stat-card">
            <h3>3</h3>
            <p>مستويات أولوية</p>
          </div>
        </div>

        <p style="font-size: 1.1em; line-height: 1.8; margin-top: 20px">
          بناءً على التحليل الشامل للمشروع الحالي، تم تحديد جميع العمليات والخدمات التي يحتاجها العملاء في تطبيق
          Flutter. يشمل هذا التقرير تفصيلاً كاملاً لجميع APIs المطلوبة مع أولوياتها وتفاصيل التنفيذ.
        </p>
      </div>

      <div class="section">
        <h2><span class="icon">🔐</span>1. نظام المصادقة والتسجيل (Authentication & Registration)</h2>

        <h3>🔑 المصادقة الأساسية</h3>
        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/register</td>
              <td><span class="method post">POST</span></td>
              <td>تسجيل عميل جديد مع التحقق من البريد الإلكتروني</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/login</td>
              <td><span class="method post">POST</span></td>
              <td>تسجيل دخول العميل مع إرجاع JWT Token</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/logout</td>
              <td><span class="method post">POST</span></td>
              <td>تسجيل خروج وإلغاء الرمز المميز</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/verify-email</td>
              <td><span class="method post">POST</span></td>
              <td>التحقق من البريد الإلكتروني</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/resend-verification</td>
              <td><span class="method post">POST</span></td>
              <td>إعادة إرسال رمز التحقق</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/forgot-password</td>
              <td><span class="method post">POST</span></td>
              <td>طلب إعادة تعيين كلمة المرور</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/reset-password</td>
              <td><span class="method post">POST</span></td>
              <td>إعادة تعيين كلمة المرور</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/change-password</td>
              <td><span class="method post">POST</span></td>
              <td>تغيير كلمة المرور للمستخدم المسجل</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📋 متطلبات التسجيل:</h4>
        <ul class="feature-list">
          <li>الاسم الكامل والبريد الإلكتروني ورقم الهاتف</li>
          <li>كلمة مرور قوية مع التأكيد</li>
          <li>اسم الشركة والعنوان (اختياري)</li>
          <li>التحقق من CAPTCHA</li>
          <li>قبول الشروط والأحكام</li>
          <li>دعم الحقول الإضافية المخصصة</li>
          <li>التحقق من البريد الإلكتروني الإجباري</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">👤</span>2. إدارة الملف الشخصي (Profile Management)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/profile</td>
              <td><span class="method get">GET</span></td>
              <td>عرض بيانات الملف الشخصي</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/profile</td>
              <td><span class="method put">PUT</span></td>
              <td>تحديث بيانات الملف الشخصي</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/profile/avatar</td>
              <td><span class="method post">POST</span></td>
              <td>رفع/تحديث صورة الملف الشخصي</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/profile/stats</td>
              <td><span class="method get">GET</span></td>
              <td>إحصائيات العميل (المهام، المعاملات، إلخ)</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/profile/delete</td>
              <td><span class="method delete">DELETE</span></td>
              <td>حذف الحساب نهائياً</td>
              <td><span class="priority low">منخفضة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📊 البيانات المتاحة في الملف الشخصي:</h4>
        <ul class="feature-list">
          <li>المعلومات الأساسية (الاسم، البريد، الهاتف)</li>
          <li>معلومات الشركة (الاسم، العنوان)</li>
          <li>الصورة الشخصية</li>
          <li>حالة الحساب والتحقق</li>
          <li>إعدادات الإشعارات</li>
          <li>معلومات البنك (للدفعات)</li>
          <li>الحقول المخصصة الإضافية</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">🏠</span>3. لوحة التحكم الرئيسية (Dashboard)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/dashboard</td>
              <td><span class="method get">GET</span></td>
              <td>بيانات لوحة التحكم الرئيسية</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/dashboard/stats</td>
              <td><span class="method get">GET</span></td>
              <td>إحصائيات مفصلة للعميل</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/dashboard/recent-activities</td>
              <td><span class="method get">GET</span></td>
              <td>الأنشطة الأخيرة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/dashboard/notifications</td>
              <td><span class="method get">GET</span></td>
              <td>الإشعارات الحديثة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📈 محتويات لوحة التحكم:</h4>
        <ul class="feature-list">
          <li>إجمالي المهام (مكتملة، قيد التنفيذ، معلقة)</li>
          <li>رصيد المحفظة الحالي</li>
          <li>المعاملات المالية الأخيرة</li>
          <li>حالة التخليص الجمركي</li>
          <li>الإشعارات غير المقروءة</li>
          <li>المهام المتاحة للمزايدة</li>
          <li>تقييم الأداء والإحصائيات</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">📋</span>4. إدارة المهام (Tasks Management)</h2>

        <h3>🚚 المهام الأساسية</h3>
        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/tasks</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة مهام العميل مع الفلترة والبحث</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks</td>
              <td><span class="method post">POST</span></td>
              <td>إنشاء مهمة جديدة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}</td>
              <td><span class="method get">GET</span></td>
              <td>تفاصيل مهمة محددة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}</td>
              <td><span class="method put">PUT</span></td>
              <td>تحديث بيانات المهمة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}/cancel</td>
              <td><span class="method post">POST</span></td>
              <td>إلغاء المهمة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}/track</td>
              <td><span class="method get">GET</span></td>
              <td>تتبع المهمة في الوقت الفعلي</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}/history</td>
              <td><span class="method get">GET</span></td>
              <td>تاريخ المهمة والتحديثات</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/tasks/{id}/rate</td>
              <td><span class="method post">POST</span></td>
              <td>تقييم السائق والخدمة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📝 أنواع المهام المدعومة:</h4>
        <ul class="feature-list">
          <li>مهمة داخلية (normal) - باستخدام task_template</li>
          <li>مهمة من الميناء (task_from) - باستخدام task_from_port_template</li>
          <li>مهمة إلى الميناء (task_to) - باستخدام task_to_port_template</li>
          <li>مهام مباشرة (تعيين سائق مباشر)</li>
          <li>مهام معلنة (advertised) - للمزايدة</li>
        </ul>

        <h4>📊 حالات المهام الفعلية:</h4>
        <ul class="feature-list">
          <li>in_progress - قيد التنفيذ</li>
          <li>advertised - معلن للمزايدة</li>
          <li>assign - مُعين لسائق</li>
          <li>started - بدأت</li>
          <li>in pickup point - في نقطة الاستلام</li>
          <li>loading - جاري التحميل</li>
          <li>in the way - في الطريق</li>
          <li>in delivery point - في نقطة التسليم</li>
          <li>unloading - جاري التفريغ</li>
          <li>completed - مكتملة</li>
          <li>canceled - ملغية</li>
          <li>refund - مرتجع</li>
          <li>invoiced - مفوتر</li>
        </ul>

        <h4>🔧 آلية إنشاء المهام:</h4>
        <ul class="feature-list">
          <li>اختيار نوع المهمة (normal/task_from/task_to)</li>
          <li>تحديد نموذج التسعير (Pricing_Template)</li>
          <li>تحديد حجم المركبة (Vehicle_Size)</li>
          <li>ملء الحقول الديناميكية من Form_Template</li>
          <li>تحديد نقاط الاستلام والتسليم</li>
          <li>اختيار طريقة التعيين (مباشر أو إعلان)</li>
          <li>حفظ البيانات في additional_data كـ JSON</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">💰</span>5. إدارة المحفظة (Wallet Management)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/wallet</td>
              <td><span class="method get">GET</span></td>
              <td>بيانات المحفظة والرصيد</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/wallet/transactions</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة المعاملات المالية</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/wallet/deposit</td>
              <td><span class="method post">POST</span></td>
              <td>إيداع أموال في المحفظة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/wallet/withdraw</td>
              <td><span class="method post">POST</span></td>
              <td>سحب أموال من المحفظة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/wallet/transfer</td>
              <td><span class="method post">POST</span></td>
              <td>تحويل أموال لعميل آخر</td>
              <td><span class="priority low">منخفضة</span></td>
            </tr>
            <tr>
              <td>/api/customer/wallet/statements</td>
              <td><span class="method get">GET</span></td>
              <td>كشف حساب مفصل</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>💳 أنواع المعاملات المالية الفعلية:</h4>
        <ul class="feature-list">
          <li><strong>credit</strong> - إيداع في المحفظة (رصيد موجب)</li>
          <li><strong>debit</strong> - خصم من المحفظة (رصيد سالب)</li>
        </ul>

        <h4>📝 بيانات المعاملة:</h4>
        <ul class="feature-list">
          <li>amount - المبلغ</li>
          <li>transaction_type - نوع المعاملة (credit/debit)</li>
          <li>description - وصف المعاملة (نص حر)</li>
          <li>image - صورة إيصال (اختياري)</li>
          <li>maturity_time - تاريخ الاستحقاق</li>
          <li>task_id - ربط بمهمة (اختياري)</li>
          <li>clearance_id - ربط بتخليص جمركي (اختياري)</li>
          <li>sequence - رقم تسلسلي للمعاملة</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">🏛️</span>6. التخليص الجمركي (Customs Clearance)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/customs-clearances</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة طلبات التخليص الجمركي</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances</td>
              <td><span class="method post">POST</span></td>
              <td>إنشاء طلب تخليص جمركي جديد</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances/{id}</td>
              <td><span class="method get">GET</span></td>
              <td>تفاصيل طلب التخليص</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances/{id}/documents</td>
              <td><span class="method post">POST</span></td>
              <td>رفع المستندات المطلوبة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances/{id}/status</td>
              <td><span class="method get">GET</span></td>
              <td>حالة التخليص الجمركي</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances/ads</td>
              <td><span class="method get">GET</span></td>
              <td>إعلانات التخليص الجمركي المتاحة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/customs-clearances/offers</td>
              <td><span class="method post">POST</span></td>
              <td>تقديم عرض للتخليص الجمركي</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📄 نظام التخليص الجمركي الفعلي:</h4>
        <ul class="feature-list">
          <li>لا يستخدم Form_Template - له نظام منفصل</li>
          <li>البيانات تُحفظ مباشرة في جدول customs_clearance</li>
          <li>نظام تاريخ العمليات في customs_clearance_history</li>
          <li>نظام العروض في customs_clearance_offers</li>
          <li>رفع الملفات والمستندات حسب الحاجة</li>
          <li>ربط مع وكلاء التخليص (is_customs_clearance_agent)</li>
        </ul>

        <h4>🔧 آلية التخليص الجمركي:</h4>
        <ul class="feature-list">
          <li>إنشاء طلب تخليص جمركي</li>
          <li>تحديد البيانات الأساسية (الوزن، القيمة، النوع)</li>
          <li>رفع المستندات المطلوبة</li>
          <li>نشر كإعلان للوكلاء</li>
          <li>استقبال العروض من الوكلاء</li>
          <li>قبول عرض وتعيين الوكيل</li>
          <li>متابعة التنفيذ والدفع</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">📢</span>7. نظام الإعلانات والمزايدات (Ads & Bidding)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/ads</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة إعلانات المهام</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads</td>
              <td><span class="method post">POST</span></td>
              <td>إنشاء إعلان مهمة جديد</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads/{id}</td>
              <td><span class="method get">GET</span></td>
              <td>تفاصيل الإعلان</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads/{id}/offers</td>
              <td><span class="method get">GET</span></td>
              <td>العروض المقدمة على الإعلان</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads/{id}/offers/{offerId}/accept</td>
              <td><span class="method post">POST</span></td>
              <td>قبول عرض محدد</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads/{id}/close</td>
              <td><span class="method post">POST</span></td>
              <td>إغلاق الإعلان</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/ads/{id}/extend</td>
              <td><span class="method post">POST</span></td>
              <td>تمديد فترة الإعلان</td>
              <td><span class="priority low">منخفضة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>🎯 مميزات نظام المزايدات:</h4>
        <ul class="feature-list">
          <li>مزايدة مفتوحة أو مغلقة</li>
          <li>تحديد الحد الأدنى للسعر</li>
          <li>فترة زمنية محددة للمزايدة</li>
          <li>تقييم العروض حسب السعر والتقييم</li>
          <li>إشعارات فورية للعروض الجديدة</li>
          <li>إمكانية التفاوض المباشر</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">💳</span>8. نظام الدفع (Payment System)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/payments/methods</td>
              <td><span class="method get">GET</span></td>
              <td>طرق الدفع المتاحة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/initiate</td>
              <td><span class="method post">POST</span></td>
              <td>بدء عملية الدفع</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/{id}/status</td>
              <td><span class="method get">GET</span></td>
              <td>حالة الدفعة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/{id}/confirm</td>
              <td><span class="method post">POST</span></td>
              <td>تأكيد الدفعة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/{id}/cancel</td>
              <td><span class="method post">POST</span></td>
              <td>إلغاء الدفعة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/history</td>
              <td><span class="method get">GET</span></td>
              <td>تاريخ المدفوعات</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/payments/{id}/receipt</td>
              <td><span class="method get">GET</span></td>
              <td>إيصال الدفع</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
          </tbody>
        </table>

        <h4>💰 بوابات الدفع المدعومة:</h4>
        <ul class="feature-list">
          <li>HyperPay (الأساسية)</li>
          <li>بطاقات الائتمان والخصم</li>
          <li>محافظ إلكترونية (STC Pay, Apple Pay)</li>
          <li>التحويل البنكي</li>
          <li>الدفع عند الاستلام</li>
          <li>رصيد المحفظة</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">🔔</span>9. نظام الإشعارات (Notifications)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/notifications</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة الإشعارات</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/notifications/{id}/read</td>
              <td><span class="method post">POST</span></td>
              <td>تحديد الإشعار كمقروء</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/notifications/mark-all-read</td>
              <td><span class="method post">POST</span></td>
              <td>تحديد جميع الإشعارات كمقروءة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/notifications/settings</td>
              <td><span class="method get">GET</span></td>
              <td>إعدادات الإشعارات</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/notifications/settings</td>
              <td><span class="method put">PUT</span></td>
              <td>تحديث إعدادات الإشعارات</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/fcm-token</td>
              <td><span class="method post">POST</span></td>
              <td>تسجيل رمز Firebase للإشعارات</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
          </tbody>
        </table>

        <h4>📱 أنواع الإشعارات:</h4>
        <ul class="feature-list">
          <li>تحديثات حالة المهام</li>
          <li>عروض جديدة على الإعلانات</li>
          <li>تحديثات المحفظة والمدفوعات</li>
          <li>تنبيهات التخليص الجمركي</li>
          <li>رسائل النظام والصيانة</li>
          <li>العروض والخصومات</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">📝</span>10. نظام الحقول الديناميكية (Dynamic Fields System)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/form-templates</td>
              <td><span class="method get">GET</span></td>
              <td>قائمة نماذج الحقول المتاحة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/form-templates/{id}/fields</td>
              <td><span class="method get">GET</span></td>
              <td>حقول نموذج محدد</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/form-fields/validate</td>
              <td><span class="method post">POST</span></td>
              <td>التحقق من صحة الحقول الديناميكية</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
          </tbody>
        </table>

        <h4>🔧 أنواع الحقول الديناميكية المدعومة:</h4>
        <ul class="feature-list">
          <li><strong>number</strong> - حقل رقمي</li>
          <li><strong>string</strong> - نص عادي</li>
          <li><strong>email</strong> - بريد إلكتروني</li>
          <li><strong>select</strong> - قائمة اختيار</li>
          <li><strong>date</strong> - تاريخ</li>
          <li><strong>file</strong> - رفع ملف</li>
          <li><strong>image</strong> - رفع صورة</li>
          <li><strong>file_expiration_date</strong> - ملف مع تاريخ انتهاء</li>
          <li><strong>file_with_text</strong> - ملف مع نص</li>
        </ul>

        <h4>📊 خصائص الحقول:</h4>
        <ul class="feature-list">
          <li>name - اسم الحقل (مفتاح فريد)</li>
          <li>label - التسمية المعروضة</li>
          <li>type - نوع الحقل</li>
          <li>required - إجباري أم لا</li>
          <li>value - القيمة الافتراضية</li>
          <li>customer_can - صلاحية العميل (write/read/hidden)</li>
          <li>driver_can - صلاحية السائق (write/read/hidden)</li>
          <li>order - ترتيب الحقل</li>
        </ul>
      </div>

      <div class="section">
        <h2><span class="icon">⚙️</span>11. الإعدادات والتكوين (Settings & Configuration)</h2>

        <table class="api-table">
          <thead>
            <tr>
              <th>API Endpoint</th>
              <th>Method</th>
              <th>الوصف</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>/api/customer/settings</td>
              <td><span class="method get">GET</span></td>
              <td>إعدادات التطبيق</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/settings</td>
              <td><span class="method put">PUT</span></td>
              <td>تحديث الإعدادات</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/vehicles</td>
              <td><span class="method get">GET</span></td>
              <td>أنواع المركبات المتاحة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/form-templates</td>
              <td><span class="method get">GET</span></td>
              <td>نماذج الطلبات المتاحة</td>
              <td><span class="priority high">عالية</span></td>
            </tr>
            <tr>
              <td>/api/customer/pricing-templates</td>
              <td><span class="method get">GET</span></td>
              <td>قوالب التسعير</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/geofences</td>
              <td><span class="method get">GET</span></td>
              <td>المناطق الجغرافية المخدومة</td>
              <td><span class="priority medium">متوسطة</span></td>
            </tr>
            <tr>
              <td>/api/customer/app-version</td>
              <td><span class="method get">GET</span></td>
              <td>معلومات إصدار التطبيق</td>
              <td><span class="priority low">منخفضة</span></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="section">
        <h2><span class="icon">📊</span>خطة التنفيذ والأولويات</h2>

        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-content">
              <h4>المرحلة الأولى - الأساسيات (أسبوع 1-2)</h4>
              <ul class="feature-list">
                <li>نظام المصادقة والتسجيل</li>
                <li>إدارة الملف الشخصي</li>
                <li>لوحة التحكم الأساسية</li>
                <li>إدارة المهام الأساسية</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-content">
              <h4>المرحلة الثانية - الخدمات المالية (أسبوع 3-4)</h4>
              <ul class="feature-list">
                <li>نظام المحفظة</li>
                <li>نظام الدفع</li>
                <li>المعاملات المالية</li>
                <li>التقارير المالية</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-content">
              <h4>المرحلة الثالثة - الخدمات المتقدمة (أسبوع 5-6)</h4>
              <ul class="feature-list">
                <li>التخليص الجمركي</li>
                <li>نظام الإعلانات والمزايدات</li>
                <li>تتبع المهام المتقدم</li>
                <li>نظام التقييمات</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-content">
              <h4>المرحلة الرابعة - التحسينات (أسبوع 7-8)</h4>
              <ul class="feature-list">
                <li>نظام الإشعارات المتقدم</li>
                <li>التحليلات والتقارير</li>
                <li>الإعدادات المتقدمة</li>
                <li>اختبار الأداء والأمان</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h2><span class="icon">🎯</span>الخلاصة والتوصيات</h2>

        <div class="stats-grid">
          <div class="stat-card">
            <h3>50+</h3>
            <p>API Endpoint</p>
          </div>
          <div class="stat-card">
            <h3>8</h3>
            <p>أسابيع للتنفيذ</p>
          </div>
          <div class="stat-card">
            <h3>4</h3>
            <p>مراحل تطوير</p>
          </div>
          <div class="stat-card">
            <h3>100%</h3>
            <p>متوافق مع المنصة</p>
          </div>
        </div>

        <h4>🔧 التقنيات المطلوبة:</h4>
        <div class="code-block">
          // Backend Technologies - Laravel 10+ with Sanctum Authentication - MySQL Database with proper indexing -
          Redis for caching and sessions - Firebase Cloud Messaging (FCM) - HyperPay Payment Gateway Integration - File
          Storage (AWS S3 or local) // API Standards - RESTful API Design - JSON Response Format - Proper HTTP Status
          Codes - Rate Limiting and Throttling - API Documentation (Swagger/OpenAPI)
        </div>

        <h4>✅ نقاط القوة في هذا التصميم المُحدث:</h4>
        <ul class="feature-list">
          <li>متوافق 100% مع آليات المنصة الحالية</li>
          <li>يستخدم نفس نماذج البيانات الموجودة</li>
          <li>يحترم نظام الحقول الديناميكية</li>
          <li>يتبع نفس منطق المعاملات المالية</li>
          <li>يدعم جميع حالات المهام الفعلية</li>
          <li>متوافق مع نظام التخليص الجمركي الحالي</li>
          <li>يستخدم نفس آلية الإعلانات والمزايدات</li>
          <li>تصميم قابل للتوسع دون تعديل البنية الحالية</li>
        </ul>

        <h4>🔧 التحديثات المُطبقة:</h4>
        <ul class="feature-list">
          <li>تصحيح أنواع المهام لتطابق النظام الحالي</li>
          <li>تبسيط المعاملات المالية إلى credit/debit فقط</li>
          <li>إزالة المستندات المُختلقة للتخليص الجمركي</li>
          <li>إضافة نظام الحقول الديناميكية الفعلي</li>
          <li>تحديث حالات المهام لتطابق الكود الحالي</li>
          <li>ضمان التوافق مع Form_Template و Form_Field</li>
        </ul>

        <div
          style="
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
          ">
          <h3 style="margin-bottom: 15px; background: none; color: white; padding: 0; box-shadow: none">
            ✅ تم التحديث والتصحيح!
          </h3>
          <p style="font-size: 1.2em; margin-bottom: 20px">
            تم تحديث التقرير ليكون متوافقاً 100% مع آليات المنصة الحالية. جميع APIs مصممة لتعمل مع النظام الموجود دون
            الحاجة لتعديلات جذرية في البنية التحتية.
          </p>
          <p style="font-size: 1.1em; opacity: 0.9">
            <strong>الآن جاهز للتنفيذ:</strong> جميع الآليات تتبع نفس منطق المنصة الحالية مع دعم كامل للحقول الديناميكية
            ونظام المعاملات المالية الموجود.
          </p>
        </div>
      </div>
    </div>
  </body>
</html>

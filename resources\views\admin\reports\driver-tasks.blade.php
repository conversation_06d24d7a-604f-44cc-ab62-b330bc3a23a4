@extends('layouts/layoutMaster')

@section('title', __('Driver Tasks Report'))

<!-- Vendor Styles -->
@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
    @vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss', 'resources/assets/vendor/libs/animate-css/animate.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss', 'resources/assets/vendor/libs/select2/select2.scss'])

@endsection

<!-- Page Styles -->
@section('page-style')
    @vite(['resources/css/app.css'])
    <style>
        /* Select2 Bootstrap 5 Compatibility */
        .select2-container--bootstrap-5 .select2-selection {
            border: 1px solid #d0d7de;
            border-radius: 0.375rem;
            min-height: calc(2.25rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .select2-container--bootstrap-5 .select2-selection:focus-within {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(2.25rem + 2px);
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding: 0;
            line-height: 1.5;
            color: #212529;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem);
            right: 0.75rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: calc(2.25rem + 2px);
            padding: 0.375rem 0.75rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            border-radius: 0.25rem;
            color: #fff;
            font-size: 0.75rem;
            margin: 0.125rem 0.25rem 0.125rem 0;
            padding: 0.25rem 0.5rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            color: #fff;
            margin-right: 0.25rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #fff;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--bootstrap-5 .select2-dropdown .select2-results__option {
            padding: 0.375rem 0.75rem;
        }

        .select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted {
            background-color: #0d6efd;
            color: #fff;
        }

        .column-selector {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }

        .column-checkbox {
            margin-right: 0.5rem;
        }

        .preview-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #dee2e6;
        }

        .info-box {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #fff;
            border-radius: 0.375rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1rem;
        }

        .info-box-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 1rem;
            color: #fff;
            font-size: 1.5rem;
        }

        .info-box-content {
            flex: 1;
        }

        .info-box-text {
            display: block;
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }

        .info-box-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 600;
            color: #495057;
        }

        .table-responsive {
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
            text-align: center;
        }

        .table td {
            text-align: center;
        }

        .btn-group .btn {
            margin-right: 0.5rem;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }

        .card-header h3 {
            margin: 0;
            font-weight: 600;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .required {
            color: #dc3545;
        }

        .export-buttons {
            text-align: center;
        }
    </style>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تقرير مهام السائق</h3>
                    </div>
                    <div class="card-body">
                        <form id="driver-tasks-form" method="POST">
                            @csrf
                            <div class="row">
                                <!-- Driver Selection -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="driver_ids">السائقين <span class="text-danger">*</span></label>
                                        <select name="driver_ids[]" id="driver_ids" class="form-control select2" multiple
                                            required>
                                            @foreach ($drivers as $driver)
                                                <option value="{{ $driver->id }}">{{ $driver->name }} -
                                                    {{ $driver->team->name ?? 'بدون فريق' }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Date Range -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_from">من تاريخ <span class="text-danger">*</span></label>
                                        <input type="date" name="date_from" id="date_from" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_to">إلى تاريخ <span class="text-danger">*</span></label>
                                        <input type="date" name="date_to" id="date_to" class="form-control" required>
                                    </div>
                                </div>

                                <!-- Task Status Filter -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="task_statuses">حالة المهمة</label>
                                        <select name="task_statuses[]" id="task_statuses" class="form-control select2"
                                            multiple>
                                            @foreach ($taskStatuses as $key => $status)
                                                <option value="{{ $key }}">{{ $status }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Payment Status Filter -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="payment_status">حالة الدفع</label>
                                        <select name="payment_status" id="payment_status" class="form-control select2">
                                            <option value="">جميع الحالات</option>
                                            @foreach ($paymentStatuses as $key => $status)
                                                <option value="{{ $key }}">{{ $status }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <!-- Payment Method Filter -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="payment_method">طريقة الدفع</label>
                                        <select name="payment_method" id="payment_method" class="form-control select2">
                                            <option value="">جميع الطرق</option>
                                            @foreach ($paymentMethods as $key => $method)
                                                <option value="{{ $key }}">{{ $method }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Column Selection -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label>الأعمدة المطلوبة في التقرير <span class="text-danger">*</span></label>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="id" id="col_id" checked>
                                                    <label class="form-check-label" for="col_id">رقم المهمة</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="customer_name" id="col_customer_name"
                                                        checked>
                                                    <label class="form-check-label" for="col_customer_name">اسم
                                                        العميل</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="driver_name" id="col_driver_name" checked>
                                                    <label class="form-check-label" for="col_driver_name">اسم السائق</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="team_name" id="col_team_name">
                                                    <label class="form-check-label" for="col_team_name">اسم الفريق</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="total_price" id="col_total_price"
                                                        checked>
                                                    <label class="form-check-label" for="col_total_price">المبلغ
                                                        الصافي</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="pickup_address" id="col_pickup_address">
                                                    <label class="form-check-label" for="col_pickup_address">عنوان
                                                        الاستلام</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="delivery_address"
                                                        id="col_delivery_address">
                                                    <label class="form-check-label" for="col_delivery_address">عنوان
                                                        التسليم</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="vehicle_name" id="col_vehicle_name">
                                                    <label class="form-check-label" for="col_vehicle_name">نوع
                                                        المركبة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="status" id="col_status">
                                                    <label class="form-check-label" for="col_status">حالة المهمة</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="payment_status" id="col_payment_status">
                                                    <label class="form-check-label" for="col_payment_status">حالة
                                                        الدفع</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="payment_method" id="col_payment_method">
                                                    <label class="form-check-label" for="col_payment_method">طريقة
                                                        الدفع</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="created_by" id="col_created_by">
                                                    <label class="form-check-label" for="col_created_by">منشئ
                                                        المهمة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="created_at" id="col_created_at">
                                                    <label class="form-check-label" for="col_created_at">تاريخ
                                                        الإنشاء</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="completed_at" id="col_completed_at">
                                                    <label class="form-check-label" for="col_completed_at">تاريخ
                                                        الإكمال</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="pickup_contact_name"
                                                        id="col_pickup_contact_name">
                                                    <label class="form-check-label" for="col_pickup_contact_name">جهة
                                                        الاتصال - الاستلام</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input column-checkbox" type="checkbox"
                                                        name="columns[]" value="delivery_contact_name"
                                                        id="col_delivery_contact_name">
                                                    <label class="form-check-label" for="col_delivery_contact_name">جهة
                                                        الاتصال - التسليم</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <button type="button" id="preview-btn" class="btn btn-info">
                                            <i class="fas fa-eye"></i> معاينة التقرير
                                        </button>
                                        <button type="button" id="export-excel-btn" class="btn btn-success" disabled>
                                            <i class="fas fa-file-excel"></i> تصدير Excel
                                        </button>
                                        <button type="button" id="export-pdf-btn" class="btn btn-danger" disabled>
                                            <i class="fas fa-file-pdf"></i> تصدير PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="row" id="preview-section" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">معاينة التقرير</h3>
                    </div>
                    <div class="card-body">
                        <!-- Summary -->
                        <div id="report-summary" class="mb-4"></div>

                        <!-- Data Table -->
                        <div class="table-responsive">
                            <table id="preview-table" class="table table-bordered table-striped">
                                <thead id="preview-thead"></thead>
                                <tbody id="preview-tbody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
    @vite(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/daterangepicker/daterangepicker.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js', 'resources/assets/vendor/libs/select2/select2.js'])

@endsection

<!-- Page Scripts -->
@section('page-script')
    <script src="{{ asset('js/admin/reports/driver-tasks.js') }}"></script>

    <script>
        // Set up routes for the JavaScript class
        window.routes = {
            preview: '{{ route('admin.reports.driver-tasks.preview') }}',
            generate: '{{ route('admin.reports.driver-tasks.generate') }}'
        };
    </script>
@endsection

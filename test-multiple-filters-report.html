<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فلاتر التقرير المتعددة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }
        .content {
            padding: 40px;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .form-select[multiple] {
            min-height: 120px;
        }
        .btn-test {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .preview-section {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin-top: 25px;
        }
        .filter-preview {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .filter-tag {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-tag {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .payment-status-tag {
            background: #fff3cd;
            color: #856404;
        }
        .payment-method-tag {
            background: #d1ecf1;
            color: #0c5460;
        }
        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }
        .selected-count {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1><i class="bi bi-funnel me-3"></i>اختبار فلاتر التقرير المتعددة</h1>
            <p class="mb-0">اختبار نموذج إنشاء التقرير مع دعم القيم المتعددة للفلاتر</p>
        </div>
        
        <div class="content">
            <!-- Report Form -->
            <div class="form-section">
                <h4><i class="bi bi-file-earmark-text me-2"></i>نموذج إنشاء التقرير</h4>
                
                <form id="reportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفترة الزمنية</label>
                                <input type="text" id="reportDateRange" class="form-control" 
                                    value="01/09/2024 - 30/09/2024" placeholder="اختر الفترة الزمنية" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">حالة المهمة</label>
                                <select id="reportStatus" class="form-select" multiple>
                                    <option value="advertised">معلن</option>
                                    <option value="in_progress">قيد التنفيذ</option>
                                    <option value="assign">مُعين</option>
                                    <option value="started">بدأت</option>
                                    <option value="in pickup point">في نقطة الاستلام</option>
                                    <option value="loading">جاري التحميل</option>
                                    <option value="in the way">في الطريق</option>
                                    <option value="in delivery point">في نقطة التسليم</option>
                                    <option value="unloading">جاري التفريغ</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="canceled">ملغية</option>
                                </select>
                                <div class="selected-count" id="statusCount">لم يتم اختيار أي حالة (جميع الحالات)</div>
                                <div class="form-text">
                                    <small class="text-muted">اضغط Ctrl/Cmd لاختيار عدة خيارات. اتركه فارغاً لجميع الحالات.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">حالة الدفع</label>
                                <select id="reportPaymentStatus" class="form-select" multiple>
                                    <option value="waiting">في الانتظار</option>
                                    <option value="pending">معلق</option>
                                    <option value="completed">مكتمل</option>
                                </select>
                                <div class="selected-count" id="paymentStatusCount">لم يتم اختيار أي حالة (جميع حالات الدفع)</div>
                                <div class="form-text">
                                    <small class="text-muted">اضغط Ctrl/Cmd لاختيار عدة خيارات. اتركه فارغاً لجميع حالات الدفع.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select id="reportPaymentMethod" class="form-select" multiple>
                                    <option value="cash">نقدي</option>
                                    <option value="credit">بطاقة ائتمان</option>
                                    <option value="banking">تحويل بنكي</option>
                                    <option value="wallet">محفظة</option>
                                </select>
                                <div class="selected-count" id="paymentMethodCount">لم يتم اختيار أي طريقة (جميع طرق الدفع)</div>
                                <div class="form-text">
                                    <small class="text-muted">اضغط Ctrl/Cmd لاختيار عدة خيارات. اتركه فارغاً لجميع طرق الدفع.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-test btn-lg" onclick="generateReport()">
                            <i class="bi bi-file-earmark-pdf me-2"></i>إنشاء التقرير
                        </button>
                    </div>
                </form>
            </div>

            <!-- Filter Preview -->
            <div class="preview-section">
                <h5><i class="bi bi-eye me-2"></i>معاينة الفلاتر المحددة</h5>
                <div class="filter-preview" id="filterPreview">
                    <div><strong>الفلاتر المطبقة:</strong></div>
                    <div id="previewContent" style="margin-top: 10px;">
                        <span style="color: #6c757d; font-style: italic;">جميع المهام (بدون فلاتر)</span>
                    </div>
                </div>
                
                <div class="alert alert-info alert-custom">
                    <h6><i class="bi bi-info-circle me-2"></i>كيفية الاستخدام:</h6>
                    <ul class="mb-0">
                        <li>اختر الفترة الزمنية المطلوبة</li>
                        <li>اختر حالة أو عدة حالات للمهام</li>
                        <li>اختر حالة أو عدة حالات للدفع</li>
                        <li>اختر طريقة أو عدة طرق للدفع</li>
                        <li>اضغط "إنشاء التقرير" لمعاينة النتيجة</li>
                    </ul>
                </div>
            </div>

            <!-- URL Preview -->
            <div class="preview-section">
                <h5><i class="bi bi-link-45deg me-2"></i>معاينة رابط التقرير</h5>
                <div class="alert alert-secondary">
                    <code id="urlPreview">customer/tasks/report?from_date=2024-09-01&to_date=2024-09-30</code>
                </div>
            </div>

            <!-- Instructions -->
            <div class="alert alert-success alert-custom">
                <h5><i class="bi bi-check-circle me-2"></i>الميزات الجديدة:</h5>
                <ul class="mb-0">
                    <li><strong>فلاتر متعددة:</strong> يمكن اختيار عدة قيم لكل فلتر</li>
                    <li><strong>عرض واضح:</strong> الفلاتر المحددة تظهر بألوان مختلفة في التقرير</li>
                    <li><strong>مرونة عالية:</strong> يمكن ترك أي فلتر فارغاً لتشمل جميع القيم</li>
                    <li><strong>واجهة محسنة:</strong> عداد للعناصر المحددة ونصائح للاستخدام</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Labels for display
        const statusLabels = {
            'advertised': 'معلن',
            'in_progress': 'قيد التنفيذ',
            'assign': 'مُعين',
            'started': 'بدأت',
            'in pickup point': 'في نقطة الاستلام',
            'loading': 'جاري التحميل',
            'in the way': 'في الطريق',
            'in delivery point': 'في نقطة التسليم',
            'unloading': 'جاري التفريغ',
            'completed': 'مكتملة',
            'canceled': 'ملغية'
        };

        const paymentStatusLabels = {
            'waiting': 'في الانتظار',
            'pending': 'معلق',
            'completed': 'مكتمل'
        };

        const paymentMethodLabels = {
            'cash': 'نقدي',
            'credit': 'بطاقة ائتمان',
            'banking': 'تحويل بنكي',
            'wallet': 'محفظة'
        };

        $(document).ready(function() {
            // Update preview when selections change
            $('#reportStatus, #reportPaymentStatus, #reportPaymentMethod').on('change', function() {
                updatePreview();
                updateCounts();
            });

            // Initial update
            updatePreview();
            updateCounts();
        });

        function updateCounts() {
            const statusCount = $('#reportStatus').val()?.length || 0;
            const paymentStatusCount = $('#reportPaymentStatus').val()?.length || 0;
            const paymentMethodCount = $('#reportPaymentMethod').val()?.length || 0;

            $('#statusCount').text(statusCount > 0 ? `تم اختيار ${statusCount} حالة` : 'لم يتم اختيار أي حالة (جميع الحالات)');
            $('#paymentStatusCount').text(paymentStatusCount > 0 ? `تم اختيار ${paymentStatusCount} حالة` : 'لم يتم اختيار أي حالة (جميع حالات الدفع)');
            $('#paymentMethodCount').text(paymentMethodCount > 0 ? `تم اختيار ${paymentMethodCount} طريقة` : 'لم يتم اختيار أي طريقة (جميع طرق الدفع)');
        }

        function updatePreview() {
            const statuses = $('#reportStatus').val() || [];
            const paymentStatuses = $('#reportPaymentStatus').val() || [];
            const paymentMethods = $('#reportPaymentMethod').val() || [];

            let previewHtml = '<div><strong>الفلاتر المطبقة:</strong></div><div style="margin-top: 10px;">';

            if (statuses.length > 0) {
                previewHtml += '<div style="margin-bottom: 5px;"><strong>حالة المهمة:</strong> ';
                statuses.forEach((status, index) => {
                    previewHtml += `<span class="filter-tag status-tag">${statusLabels[status]}</span>`;
                    if (index < statuses.length - 1) previewHtml += ' ';
                });
                previewHtml += '</div>';
            }

            if (paymentStatuses.length > 0) {
                previewHtml += '<div style="margin-bottom: 5px;"><strong>حالة الدفع:</strong> ';
                paymentStatuses.forEach((status, index) => {
                    previewHtml += `<span class="filter-tag payment-status-tag">${paymentStatusLabels[status]}</span>`;
                    if (index < paymentStatuses.length - 1) previewHtml += ' ';
                });
                previewHtml += '</div>';
            }

            if (paymentMethods.length > 0) {
                previewHtml += '<div style="margin-bottom: 5px;"><strong>طريقة الدفع:</strong> ';
                paymentMethods.forEach((method, index) => {
                    previewHtml += `<span class="filter-tag payment-method-tag">${paymentMethodLabels[method]}</span>`;
                    if (index < paymentMethods.length - 1) previewHtml += ' ';
                });
                previewHtml += '</div>';
            }

            if (statuses.length === 0 && paymentStatuses.length === 0 && paymentMethods.length === 0) {
                previewHtml += '<span style="color: #6c757d; font-style: italic;">جميع المهام (بدون فلاتر)</span>';
            }

            previewHtml += '</div>';
            $('#previewContent').html(previewHtml);

            // Update URL preview
            updateUrlPreview();
        }

        function updateUrlPreview() {
            const params = new URLSearchParams();
            params.append('from_date', '2024-09-01');
            params.append('to_date', '2024-09-30');

            const statuses = $('#reportStatus').val() || [];
            const paymentStatuses = $('#reportPaymentStatus').val() || [];
            const paymentMethods = $('#reportPaymentMethod').val() || [];

            statuses.forEach(status => {
                if (status) params.append('status[]', status);
            });

            paymentStatuses.forEach(paymentStatus => {
                if (paymentStatus) params.append('payment_status[]', paymentStatus);
            });

            paymentMethods.forEach(paymentMethod => {
                if (paymentMethod) params.append('payment_method[]', paymentMethod);
            });

            $('#urlPreview').text(`customer/tasks/report?${params.toString()}`);
        }

        function generateReport() {
            const statuses = $('#reportStatus').val() || [];
            const paymentStatuses = $('#reportPaymentStatus').val() || [];
            const paymentMethods = $('#reportPaymentMethod').val() || [];

            let message = 'تم إنشاء التقرير بنجاح!\n\n';
            message += 'الفلاتر المطبقة:\n';
            
            if (statuses.length > 0) {
                message += `• حالة المهمة: ${statuses.map(s => statusLabels[s]).join('، ')}\n`;
            } else {
                message += '• حالة المهمة: جميع الحالات\n';
            }
            
            if (paymentStatuses.length > 0) {
                message += `• حالة الدفع: ${paymentStatuses.map(s => paymentStatusLabels[s]).join('، ')}\n`;
            } else {
                message += '• حالة الدفع: جميع الحالات\n';
            }
            
            if (paymentMethods.length > 0) {
                message += `• طريقة الدفع: ${paymentMethods.map(m => paymentMethodLabels[m]).join('، ')}\n`;
            } else {
                message += '• طريقة الدفع: جميع الطرق\n';
            }

            alert(message);
        }
    </script>
</body>
</html>

* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  body {
    padding: 0;
    margin: 0;
  }

  #notfound {
    position: relative;
    height: 100vh;
  }

  #notfound .notfound {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
    text-align: left;
  }

  .notfound {
    max-width: 560px;
    width: 100%;
    padding-left: 160px;
    line-height: 1.1;
  }

  .notfound .notfound-404 {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    width: 140px;
    height: 140px;
    background-image: url('../img/emoji.png');
    background-size: cover;
  }

  .notfound .notfound-404:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-transform: scale(2.4);
        -ms-transform: scale(2.4);
            transform: scale(2.4);
    border-radius: 50%;
    background-color: #f2f5f8;
    z-index: -1;
  }

  .notfound h1 {
    font-family: 'Nunito', sans-serif;
    font-size: 65px;
    font-weight: 700;
    margin-top: 0px;
    margin-bottom: 10px;
    color: #151723;
    text-transform: uppercase;
  }

  .notfound h2 {
    font-family: 'Nunito', sans-serif;
    font-size: 21px;
    font-weight: 400;
    margin: 0;
    text-transform: uppercase;
    color: #151723;
  }

  .notfound p {
    font-family: 'Nunito', sans-serif;
    color: #999fa5;
    font-weight: 400;
    margin-top: 10px;
  }

  .notfound a {
    font-family: 'Nunito', sans-serif;
    display: inline-block;
    font-weight: 700;
    border-radius: 40px;
    text-decoration: none;
    color: #388dbc;
    margin-top: 20px;
  }

  /* تحسين التناسق للشاشات الصغيرة */
  @media only screen and (max-width: 767px) {
    .notfound {
      padding-left: 10px;
      text-align: center;
    }

    .notfound .notfound-404 {
      position: relative;
      margin: 0 auto 20px auto;
      width: 100px;
      height: 100px;
      left: 40%;
    }

    .notfound h1 {
      font-size: 50px;
      text-align: center;
    }

    .notfound h2 {
      font-size: 18px;
      text-align: center;
    }

    .notfound p {
      font-size: 16px;
      text-align: center;
    }
  }

  /* تحسين التناسق للشاشات الصغيرة جدًا */
  @media only screen and (max-width: 480px) {
    .notfound .notfound-404 {
      width: 80px;
      height: 80px;
    }

    .notfound h1 {
      font-size: 36px;
    }

    .notfound h2 {
      font-size: 14px;
    }

    .notfound p {
      font-size: 12px;
    }

    .notfound {
      padding-left: 5px;
      padding-right: 5px;
    }
  }

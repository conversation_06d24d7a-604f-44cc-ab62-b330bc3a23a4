<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Profile API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-put {
        background: #ffc107;
        color: #212529;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      .auth-required {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .auth-icon {
        color: #856404;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Profile API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-user"></i> Driver Profile API</h1>
        <p>إدارة ملف السائق الشخصي وتحديث البيانات</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <div class="auth-required">
        <i class="fas fa-lock auth-icon"></i>
        <strong>مصادقة مطلوبة:</strong> جميع endpoints في هذا القسم تتطلب Bearer Token في header للمصادقة.
      </div>

      <!-- Get Profile Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">عرض الملف الشخصي</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/profile</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على بيانات الملف الشخصي للسائق المصادق عليه مع العلاقات المرتبطة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-key"></i> المصادقة</h3>
          <pre><code class="language-http">Authorization: Bearer {token}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Profile retrieved successfully",
    "data": {
        "driver": {
            "id": 1,
            "name": "أحمد محمد السعيد",
            "email": "<EMAIL>",
            "phone": "501234567",
            "phone_code": "+966",
            "address": "الرياض، المملكة العربية السعودية",
            "image": "/storage/drivers/profile.jpg",
            "status": "active",
            "online": true,
            "free": true,
            "team_id": 1,
            "vehicle_size_id": 2,
            "commission_type": "percentage",
            "commission_value": 15.0,
            "last_activity_at": "2024-01-15T14:30:00.000000Z",
            "app_version": "1.0.0",
            "additional_data": {
                "national_id": "**********",
                "license_number": "987654321"
            },
            "created_at": "2024-01-01T10:00:00.000000Z",
            "updated_at": "2024-01-15T14:30:00.000000Z",
            "team": {
                "id": 1,
                "name": "فريق الرياض"
            },
            "vehicle_size": {
                "id": 2,
                "name": "شاحنة صغيرة - مفتوحة - 3 طن",
                "description": "حمولة 3000 كيلو"
            }
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Update Profile Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-put">PUT</span>
          <h2 class="method-title">تحديث الملف الشخصي</h2>
        </div>

        <div class="endpoint"><strong>PUT</strong> /api/driver/profile</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحدث بيانات الملف الشخصي للسائق مع إمكانية رفع صورة جديدة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>name</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>اسم السائق الكامل (255 حرف كحد أقصى)</td>
              </tr>
              <tr>
                <td><code>email</code></td>
                <td>email</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>البريد الإلكتروني (فريد)</td>
              </tr>
              <tr>
                <td><code>phone</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>رقم الهاتف (20 حرف كحد أقصى)</td>
              </tr>
              <tr>
                <td><code>address</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>العنوان (500 حرف كحد أقصى)</td>
              </tr>
              <tr>
                <td><code>image</code></td>
                <td>file</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>صورة الملف الشخصي (jpeg,png,jpg,gif - 2MB كحد أقصى)</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "name": "أحمد محمد السعيد المحدث",
    "email": "<EMAIL>",
    "phone": "501234568",
    "address": "الرياض، حي النرجس، المملكة العربية السعودية"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Profile updated successfully",
    "data": {
        "driver": {
            "id": 1,
            "name": "أحمد محمد السعيد المحدث",
            "email": "<EMAIL>",
            "phone": "501234568",
            "address": "الرياض، حي النرجس، المملكة العربية السعودية",
            "image": "/storage/drivers/updated_profile.jpg",
            "updated_at": "2024-01-15T15:45:00.000000Z"
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Change Password Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تغيير كلمة المرور</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/change-password</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يسمح للسائق بتغيير كلمة المرور الحالية بعد التحقق من كلمة المرور القديمة.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>current_password</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>كلمة المرور الحالية</td>
              </tr>
              <tr>
                <td><code>new_password</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>كلمة المرور الجديدة (8 أحرف على الأقل)</td>
              </tr>
              <tr>
                <td><code>new_password_confirmation</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>تأكيد كلمة المرور الجديدة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "current_password": "oldpassword123",
    "new_password": "newpassword456",
    "new_password_confirmation": "newpassword456"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Password changed successfully"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-exclamation-triangle"></i> أخطاء محتملة</h3>

          <div class="response-example">
            <span class="status-code status-422">422 Validation Error</span>
            <strong>كلمة مرور حالية خاطئة:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "current_password": ["The current password is incorrect."]
    }
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Get Stats Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-get">GET</span>
          <h2 class="method-title">إحصائيات السائق</h2>
        </div>

        <div class="endpoint"><strong>GET</strong> /api/driver/profile/stats</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحصل على إحصائيات شاملة للسائق مثل عدد المهام المكتملة، التقييمات، والأرباح.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Stats retrieved successfully",
    "data": {
        "stats": {
            "total_tasks": 156,
            "completed_tasks": 142,
            "cancelled_tasks": 8,
            "pending_tasks": 6,
            "completion_rate": 91.0,
            "average_rating": 4.7,
            "total_ratings": 128,
            "total_earnings": 15420.50,
            "this_month_earnings": 2340.75,
            "this_week_earnings": 580.25,
            "online_hours_today": 8.5,
            "online_hours_this_week": 42.3,
            "last_task_completed": "2024-01-15T13:45:00.000000Z",
            "member_since": "2024-01-01T10:00:00.000000Z"
        }
    }
}</code></pre>
        </div>
      </div>

      <!-- Update Additional Data Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تحديث البيانات الإضافية</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/profile/additional-data</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يحدث البيانات الإضافية للسائق مثل رقم الهوية، رخصة القيادة، وغيرها من المعلومات.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات</h3>
          <p>يقبل أي مفاتيح JSON صالحة كبيانات إضافية:</p>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مثال</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>national_id</code></td>
                <td>string</td>
                <td>رقم الهوية الوطنية</td>
              </tr>
              <tr>
                <td><code>license_number</code></td>
                <td>string</td>
                <td>رقم رخصة القيادة</td>
              </tr>
              <tr>
                <td><code>license_expiry</code></td>
                <td>date</td>
                <td>تاريخ انتهاء الرخصة</td>
              </tr>
              <tr>
                <td><code>emergency_contact</code></td>
                <td>string</td>
                <td>جهة الاتصال في الطوارئ</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "national_id": "**********",
    "license_number": "987654321",
    "license_expiry": "2025-12-31",
    "emergency_contact": "966501234567",
    "blood_type": "O+",
    "notes": "سائق محترف مع خبرة 5 سنوات"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Additional data updated successfully",
    "data": {
        "additional_data": {
            "national_id": "**********",
            "license_number": "987654321",
            "license_expiry": "2025-12-31",
            "emergency_contact": "966501234567",
            "blood_type": "O+",
            "notes": "سائق محترف مع خبرة 5 سنوات"
        }
    }
}</code></pre>
        </div>
      </div>

      <div class="section">
        <h3><i class="fas fa-book"></i> الكود المصدري</h3>
        <p>يمكن العثور على الكود المصدري في:</p>
        <pre><code class="language-php">app/Http/Controllers/Api/DriverProfileController.php</code></pre>

        <div class="note">
          <i class="fas fa-code note-icon"></i>
          <strong>الدوال الرئيسية:</strong>
          <ul style="margin-top: 10px">
            <li><code>show(Request $request)</code> - عرض الملف الشخصي</li>
            <li><code>update(Request $request)</code> - تحديث الملف الشخصي</li>
            <li><code>changePassword(Request $request)</code> - تغيير كلمة المرور</li>
            <li><code>getStats(Request $request)</code> - الحصول على الإحصائيات</li>
            <li><code>updateAdditionalData(Request $request)</code> - تحديث البيانات الإضافية</li>
          </ul>
        </div>

        <div class="note">
          <i class="fas fa-info-circle note-icon"></i>
          <strong>ملاحظات مهمة:</strong>
          <ul style="margin-top: 10px">
            <li>جميع endpoints تتطلب مصادقة Sanctum</li>
            <li>يتم التحقق من صحة البيانات قبل التحديث</li>
            <li>الصور يتم رفعها إلى مجلد storage/drivers</li>
            <li>البيانات الإضافية يتم حفظها كـ JSON في قاعدة البيانات</li>
            <li>الإحصائيات يتم حسابها في الوقت الفعلي</li>
          </ul>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

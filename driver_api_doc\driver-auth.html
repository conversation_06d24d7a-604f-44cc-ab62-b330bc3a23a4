<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Authentication API - SafeDests</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .nav-breadcrumb {
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-breadcrumb a {
        color: #667eea;
        text-decoration: none;
        margin-right: 10px;
      }

      .nav-breadcrumb a:hover {
        text-decoration: underline;
      }

      .method-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #667eea;
      }

      .method-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }

      .method-badge {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9rem;
      }

      .method-post {
        background: #28a745;
        color: white;
      }

      .method-get {
        background: #007bff;
        color: white;
      }

      .method-put {
        background: #ffc107;
        color: #212529;
      }

      .method-delete {
        background: #dc3545;
        color: white;
      }

      .method-title {
        font-size: 1.5rem;
        color: #2c3e50;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin-bottom: 20px;
        border-left: 4px solid #667eea;
      }

      .section {
        margin-bottom: 25px;
      }

      .section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.2rem;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 5px;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      .param-table th,
      .param-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
      }

      .param-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .param-required {
        color: #dc3545;
        font-weight: bold;
      }

      .param-optional {
        color: #6c757d;
      }

      pre {
        background: #2d3748 !important;
        border-radius: 8px;
        padding: 20px;
        overflow-x: auto;
        margin: 15px 0;
      }

      code {
        font-family: 'Fira Code', 'Courier New', monospace;
        font-size: 0.9rem;
      }

      .response-example {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .status-code {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        margin-left: 10px;
      }

      .status-200 {
        background: #d4edda;
        color: #155724;
      }
      .status-201 {
        background: #d4edda;
        color: #155724;
      }
      .status-400 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-401 {
        background: #f8d7da;
        color: #721c24;
      }
      .status-422 {
        background: #fff3cd;
        color: #856404;
      }
      .status-500 {
        background: #f8d7da;
        color: #721c24;
      }

      .back-btn {
        display: inline-block;
        padding: 12px 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        margin-bottom: 30px;
        transition: all 0.3s ease;
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }

      .note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }

      .note-icon {
        color: #0066cc;
        margin-left: 10px;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .method-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="nav-breadcrumb">
        <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
        <i class="fas fa-chevron-left"></i>
        <span>Driver Authentication API</span>
      </div>

      <div class="header">
        <h1><i class="fas fa-sign-in-alt"></i> Driver Authentication API</h1>
        <p>إدارة تسجيل الدخول والخروج وإعادة تعيين كلمة المرور للسائقين</p>
      </div>

      <a href="index.html" class="back-btn"> <i class="fas fa-arrow-right"></i> العودة للرئيسية </a>

      <!-- Login Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تسجيل الدخول</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/login</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>
            يسمح للسائق بتسجيل الدخول باستخدام البريد الإلكتروني أو اسم المستخدم وكلمة المرور. يقوم بإنشاء Sanctum token
            للمصادقة.
          </p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>login</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>البريد الإلكتروني أو اسم المستخدم</td>
              </tr>
              <tr>
                <td><code>password</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>كلمة المرور (6 أحرف على الأقل)</td>
              </tr>
              <tr>
                <td><code>device_name</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>اسم الجهاز (255 حرف كحد أقصى)</td>
              </tr>
              <tr>
                <td><code>device_id</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>معرف الجهاز الفريد</td>
              </tr>
              <tr>
                <td><code>fcm_token</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>رمز Firebase للإشعارات</td>
              </tr>
              <tr>
                <td><code>app_version</code></td>
                <td>string</td>
                <td><span class="param-optional">اختياري</span></td>
                <td>إصدار التطبيق</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-code"></i> مثال على الطلب</h3>
          <pre><code class="language-json">{
    "login": "<EMAIL>",
    "password": "password123",
    "device_name": "iPhone 14 Pro",
    "device_id": "ABC123XYZ",
    "fcm_token": "fcm_token_here",
    "app_version": "1.0.0"
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Login successful",
    "data": {
        "driver": {
            "id": 1,
            "name": "أحمد محمد",
            "email": "<EMAIL>",
            "phone": "966501234567",
            "status": "active",
            "online": true,
            "free": true,
            "team_id": 1,
            "vehicle_size_id": 2
        },
        "token": "1|abc123def456...",
        "token_type": "Bearer"
    }
}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-exclamation-triangle"></i> أخطاء محتملة</h3>

          <div class="response-example">
            <span class="status-code status-401">401 Unauthorized</span>
            <strong>بيانات دخول خاطئة:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Invalid credentials"
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-403">403 Forbidden</span>
            <strong>حساب غير مفعل:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Driver account is not active"
}</code></pre>
          </div>

          <div class="response-example">
            <span class="status-code status-422">422 Validation Error</span>
            <strong>خطأ في التحقق:</strong>
            <pre><code class="language-json">{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "login": ["The login field is required."],
        "password": ["The password must be at least 6 characters."]
    }
}</code></pre>
          </div>
        </div>

        <div class="note">
          <i class="fas fa-info-circle note-icon"></i>
          <strong>ملاحظة:</strong> يتم إلغاء جميع الرموز المميزة السابقة للجهاز نفسه عند تسجيل الدخول لضمان جلسة واحدة
          فقط لكل جهاز.
        </div>
      </div>

      <!-- Logout Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تسجيل الخروج</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/logout</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يقوم بتسجيل خروج السائق وإلغاء الرمز المميز الحالي. يتطلب مصادقة Sanctum.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-key"></i> المصادقة</h3>
          <p>يتطلب Bearer Token في header:</p>
          <pre><code class="language-http">Authorization: Bearer {token}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Logged out successfully"
}</code></pre>
        </div>
      </div>

      <!-- Reset Password Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">إعادة تعيين كلمة المرور</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/reset-password</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يرسل رابط إعادة تعيين كلمة المرور إلى البريد الإلكتروني للسائق.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-cogs"></i> المعاملات المطلوبة</h3>
          <table class="param-table">
            <thead>
              <tr>
                <th>المعامل</th>
                <th>النوع</th>
                <th>مطلوب</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>email</code></td>
                <td>string</td>
                <td><span class="param-required">مطلوب</span></td>
                <td>البريد الإلكتروني المسجل</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Password reset link sent to your email"
}</code></pre>
        </div>
      </div>

      <!-- Refresh Token Method -->
      <div class="method-card">
        <div class="method-header">
          <span class="method-badge method-post">POST</span>
          <h2 class="method-title">تحديث الرمز المميز</h2>
        </div>

        <div class="endpoint"><strong>POST</strong> /api/driver/refresh-token</div>

        <div class="section">
          <h3><i class="fas fa-info-circle"></i> الوصف</h3>
          <p>يقوم بتحديث الرمز المميز الحالي وإنشاء رمز جديد. يتطلب مصادقة Sanctum.</p>
        </div>

        <div class="section">
          <h3><i class="fas fa-key"></i> المصادقة</h3>
          <p>يتطلب Bearer Token في header:</p>
          <pre><code class="language-http">Authorization: Bearer {token}</code></pre>
        </div>

        <div class="section">
          <h3><i class="fas fa-check-circle"></i> الاستجابة الناجحة</h3>
          <span class="status-code status-200">200 OK</span>
          <pre><code class="language-json">{
    "success": true,
    "message": "Token refreshed successfully",
    "data": {
        "token": "2|xyz789abc123...",
        "token_type": "Bearer"
    }
}</code></pre>
        </div>
      </div>

      <div class="section">
        <h3><i class="fas fa-book"></i> الكود المصدري</h3>
        <p>يمكن العثور على الكود المصدري في:</p>
        <pre><code class="language-php">app/Http/Controllers/Api/DriverAuthController.php</code></pre>

        <div class="note">
          <i class="fas fa-code note-icon"></i>
          <strong>الدوال الرئيسية:</strong>
          <ul style="margin-top: 10px">
            <li><code>login(Request $request)</code> - تسجيل الدخول</li>
            <li><code>logout(Request $request)</code> - تسجيل الخروج</li>
            <li><code>resetPassword(Request $request)</code> - إعادة تعيين كلمة المرور</li>
            <li><code>refreshToken(Request $request)</code> - تحديث الرمز المميز</li>
          </ul>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </body>
</html>

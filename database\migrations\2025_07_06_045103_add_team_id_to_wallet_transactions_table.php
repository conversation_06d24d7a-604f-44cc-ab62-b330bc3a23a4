<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table('wallet_transactions', function (Blueprint $table) {
      $table->unsignedBigInteger('team_id')->nullable()->after('task_id');
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table('wallet_transactions', function (Blueprint $table) {
      $table->dropColumn('team_id');
    });
  }
};

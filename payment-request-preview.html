<!doctype html>
<html dir="rtl" lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>معاينة طلب السداد</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap"
      rel="stylesheet" />
    <link rel="stylesheet" href="resources/css/payment-request-print.css" />
    <style>
      .preview-container {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
      }
      .preview-controls {
        text-align: center;
        margin-bottom: 30px;
      }
      .btn {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin: 0 10px;
        font-size: 16px;
      }
      .btn:hover {
        background-color: #0056b3;
      }
      .preview-frame {
        border: 2px solid #ddd;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <div class="preview-container">
      <h1 style="text-align: center; color: #007bff; margin-bottom: 30px">معاينة طلب السداد الرسمي</h1>

      <div class="preview-controls">
        <button class="btn" onclick="printDocument()">طباعة المستند</button>
        <button class="btn" onclick="window.location.reload()">إعادة تحميل</button>
      </div>

      <div class="preview-frame">
        <div class="payment-request-container">
          <!-- Reference Number -->
          <div class="payment-request-reference-number">المرجع: 1234520240830456</div>

          <!-- Company Letterhead -->
          <div class="payment-request-letterhead">
            <div class="payment-request-company-name">شركة النقل الآمن</div>
            <div class="payment-request-company-subtitle">خدمات النقل والتوصيل المتميزة</div>
            <div class="payment-request-document-title">طلب سداد مالي</div>
            <div class="payment-request-document-number">رقم الطلب: 1234520240830456</div>
          </div>

          <!-- Date Section -->
          <div class="payment-request-date-section">التاريخ: 30/08/2024</div>

          <!-- Employee Information -->
          <div class="payment-request-section">
            <table class="payment-request-table">
              <tr>
                <td class="label-cell">اسم الموظف طالب السداد</td>
                <td class="value-cell">أحمد محمد علي</td>
              </tr>
            </table>
          </div>

          <!-- Payment Amount Section -->
          <div class="payment-request-section">
            <div class="payment-request-section-title">بيانات السداد</div>
            <div class="payment-request-amount-section">
              <div class="payment-request-amount-title">مبلغ السداد</div>
              <div class="payment-request-amount-words">خمسمائة ريال</div>
            </div>
          </div>

          <!-- Payment Breakdown -->
          <div class="payment-request-section">
            <div class="payment-request-section-title">السداد</div>
            <table class="payment-request-table">
              <tr>
                <td class="label-cell">دفعة</td>
                <td class="value-cell">500.00 ريال</td>
              </tr>
              <tr>
                <td class="label-cell">باقي حساب</td>
                <td class="value-cell">350.00 ريال</td>
              </tr>
              <tr>
                <td class="label-cell">الحساب كامل</td>
                <td class="value-cell">850.00 ريال</td>
              </tr>
            </table>
          </div>

          <!-- Bank Information -->
          <div class="payment-request-section">
            <div class="payment-request-section-title">بيانات البنك</div>
            <table class="payment-request-table">
              <tr>
                <td class="label-cell">اسم البنك</td>
                <td class="value-cell">البنك الأهلي السعودي</td>
              </tr>
              <tr>
                <td class="label-cell">رقم الحساب</td>
                <td class="value-cell">**********</td>
              </tr>
              <tr>
                <td class="label-cell">رقم الآيبان</td>
                <td class="value-cell">SA12 3456 7890 1234 5678 90</td>
              </tr>
              <tr>
                <td class="label-cell">اسم المورد</td>
                <td class="value-cell">محمد أحمد السائق</td>
              </tr>
            </table>
          </div>

          <!-- Trip Information -->
          <div class="payment-request-section">
            <div class="payment-request-section-title">بيانات الرحلة</div>
            <table class="payment-request-table">
              <tr>
                <td class="label-cell">رقم المهمة</td>
                <td class="value-cell">#12345</td>
              </tr>
              <tr>
                <td class="label-cell">اسم صاحب الرحلة</td>
                <td class="value-cell">عبدالله أحمد</td>
              </tr>
              <tr>
                <td class="label-cell">جهة الرحلة</td>
                <td class="value-cell">من الرياض - حي النخيل<br />إلى جدة - حي الصفا</td>
              </tr>
            </table>
          </div>

          <!-- Important Note -->
          <div class="payment-request-important-note">
            <strong>ملاحظة مهمة:</strong> يرجى التأكد من صحة جميع البيانات المذكورة أعلاه قبل الموافقة على السداد.
          </div>

          <!-- Approval Section -->
          <div class="payment-request-approval-section">
            <div class="payment-request-approval-title">قسم الموافقات والاعتمادات</div>
            <table class="payment-request-signature-table">
              <tr>
                <td>
                  <strong>إعداد الطلب</strong><br />
                  <small>قسم العمليات</small><br /><br /><br />
                  التوقيع: _______________<br />
                  التاريخ: _______________
                </td>
                <td>
                  <strong>مراجعة المحاسب</strong><br />
                  <small>القسم المالي</small><br /><br /><br />
                  التوقيع: _______________<br />
                  التاريخ: _______________
                </td>
                <td>
                  <strong>اعتماد المدير</strong><br />
                  <small>الإدارة العليا</small><br /><br /><br />
                  التوقيع: _______________<br />
                  التاريخ: _______________
                </td>
              </tr>
            </table>
          </div>

          <!-- Footer -->
          <div class="payment-request-footer">
            <p>
              <strong>شركة النقل الآمن</strong> | هاتف: +966-XX-XXX-XXXX | البريد الإلكتروني: <EMAIL>
            </p>
            <p>هذا المستند تم إنشاؤه إلكترونياً في 30/08/2024 10:30:00 ص</p>
          </div>
        </div>
      </div>
    </div>

    <script>
      function printDocument() {
        window.print();
      }

      // Generate dynamic reference number on page load
      document.addEventListener('DOMContentLoaded', function () {
        const today = new Date();
        const taskId = 12345; // Example task ID
        const dateString =
          today.getFullYear().toString() +
          (today.getMonth() + 1).toString().padStart(2, '0') +
          today.getDate().toString().padStart(2, '0');
        const randomNumber = Math.floor(Math.random() * 900) + 100;
        const referenceNumber = `${taskId}${dateString}${randomNumber}`;

        // Update reference numbers in the document
        document
          .querySelectorAll('.payment-request-reference-number, .payment-request-document-number')
          .forEach(function (element) {
            if (element.classList.contains('payment-request-reference-number')) {
              element.textContent = `المرجع: ${referenceNumber}`;
            } else {
              element.textContent = `رقم الطلب: ${referenceNumber}`;
            }
          });
      });
    </script>
  </body>
</html>

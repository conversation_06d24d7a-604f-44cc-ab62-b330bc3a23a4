<!doctype html>
<html dir="rtl" lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تحليل متطلبات طلب السحب النقدي لمحفظة الفريق</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap"
      rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <style>
      body {
        font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
      }
      .analysis-container {
        max-width: 1400px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 30px;
        text-align: center;
      }
      .header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
      }
      .content {
        padding: 40px;
      }
      .section-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #007bff;
      }
      .section-card.understanding {
        border-left-color: #28a745;
        background: #e8f5e8;
      }
      .section-card.requirements {
        border-left-color: #ffc107;
        background: #fff8e1;
      }
      .section-card.technical {
        border-left-color: #dc3545;
        background: #f8e8e8;
      }
      .section-card.implementation {
        border-left-color: #6f42c1;
        background: #f3e8ff;
      }
      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 15px 0;
      }
      .database-table {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
      }
      .comparison-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
      }
      .comparison-item {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
      }
      .comparison-item.driver {
        border-color: #28a745;
      }
      .comparison-item.team {
        border-color: #007bff;
      }
      .step-item {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
        position: relative;
      }
      .step-number {
        position: absolute;
        top: -15px;
        right: 20px;
        background: #007bff;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .feature-list {
        list-style: none;
        padding: 0;
      }
      .feature-list li {
        padding: 8px 0;
        position: relative;
        padding-right: 25px;
      }
      .feature-list li::before {
        content: '✓';
        position: absolute;
        right: 0;
        color: #28a745;
        font-weight: bold;
      }
      .alert-custom {
        border-radius: 8px;
        border: none;
        padding: 15px 20px;
      }
      .badge-custom {
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
      }
      .workflow-diagram {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
      }
      .entity-box {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        margin: 5px;
        font-weight: 600;
      }
      .arrow {
        font-size: 1.5rem;
        color: #6c757d;
        margin: 0 10px;
      }
    </style>
  </head>
  <body>
    <div class="analysis-container">
      <div class="header">
        <h1><i class="bi bi-clipboard-data me-3"></i>تحليل متطلبات طلب السحب النقدي لمحفظة الفريق</h1>
        <p class="mb-0">تقرير مفصل لتطبيق نظام طلبات السحب النقدي على محافظ الفرق</p>
      </div>

      <div class="content">
        <!-- فهم المتطلبات -->
        <div class="section-card understanding">
          <h4><i class="bi bi-lightbulb-fill text-success me-2"></i>فهم المتطلبات</h4>

          <h6>📋 المطلوب الأساسي:</h6>
          <ul class="feature-list">
            <li>تطبيق نظام طلب السحب النقدي على محفظة الفريق (team_wallet)</li>
            <li>إضافة الخيار في صفحة views/admin/teams/wallets/index.blade.php</li>
            <li>استخدام بيانات رئيس الفريق (User) بدلاً من السائق</li>
            <li>تصميم مطابق لنظام محفظة السائق</li>
            <li>إنشاء جدول logs منفصل لمحفظة الفريق</li>
          </ul>

          <h6>🔍 التحليل الحالي للنظام:</h6>
          <div class="comparison-grid">
            <div class="comparison-item driver">
              <h6>محفظة السائق (موجودة)</h6>
              <ul class="feature-list">
                <li>جدول: wallets</li>
                <li>معاملات: wallet_transactions</li>
                <li>سجلات: wallet_payment_request_logs</li>
                <li>البيانات: من Driver model</li>
                <li>البنك: driver->bank_name, account_number, iban_number</li>
              </ul>
            </div>
            <div class="comparison-item team">
              <h6>محفظة الفريق (مطلوبة)</h6>
              <ul class="feature-list">
                <li>جدول: team_wallet</li>
                <li>معاملات: team_wallet_transactions</li>
                <li>سجلات: team_wallet_payment_request_logs (جديد)</li>
                <li>البيانات: من Team Leader (User)</li>
                <li>البنك: user->bank_name, account_number, iban_number</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- آلية تحديد رئيس الفريق -->
        <div class="section-card requirements">
          <h4><i class="bi bi-person-check-fill text-success me-2"></i>آلية تحديد رئيس الفريق (محدثة)</h4>

          <div class="alert alert-success">
            <strong>توضيح من المستخدم:</strong> رئيس الفريق يُحدد من جدول user_has_teams - أول مستخدم مرتبط بالفريق هو
            رئيس الفريق
          </div>

          <h6>🔍 الوضع الحالي المحدث:</h6>
          <div class="code-block">
            // جدول user_has_teams - الهيكل الحالي Schema::create('user_has_teams', function (Blueprint $table) {
            $table->id(); // يوفر ترتيب تلقائي $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('team_id'); $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('team_id')->references('id')->on('teams'); }); // العلاقة في Teams model public function
            users() { return $this->hasMany(User_Teams::class, 'team_id')->with('user'); }
          </div>

          <h6>✅ الحل المطبق:</h6>
          <div class="step-item">
            <div class="step-number">1</div>
            <h6>استخدام أول مستخدم كرئيس فريق</h6>
            <p>الحصول على أول مستخدم مرتبط بالفريق بناءً على ترتيب الـ id في user_has_teams</p>
            <div class="code-block">
              // في Teams model - إضافة method جديد public function getTeamLeaderAttribute() { $firstUserTeam =
              $this->users()->orderBy('id', 'asc')->first(); return $firstUserTeam ? $firstUserTeam->user : null; } //
              أو باستخدام العلاقة المباشرة public function teamLeader() { return $this->users()->orderBy('id',
              'asc')->first()?->user; }
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <h6>التحقق من وجود رئيس فريق</h6>
            <p>منع إنشاء طلب سحب نقدي إذا لم يكن للفريق رئيس</p>
            <div class="code-block">
              // في Controller - التحقق قبل السماح بطلب السحب public function checkTeamLeaderExists($teamId) { $team =
              Teams::find($teamId); if (!$team || !$team->teamLeader) { return response()->json([ 'status' => 0, 'error'
              => 'لا يمكن إنشاء طلب سحب نقدي - الفريق لا يملك رئيس فريق' ]); } return true; }
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">3</div>
            <h6>استخدام بيانات رئيس الفريق</h6>
            <p>استخدام البيانات البنكية والشخصية لرئيس الفريق في طلب السحب</p>
            <div class="code-block">
              // الحصول على بيانات رئيس الفريق $teamLeader = $team->teamLeader; $bankName = $teamLeader->bank_name;
              $accountNumber = $teamLeader->account_number; $ibanNumber = $teamLeader->iban_number; $leaderName =
              $teamLeader->name;
            </div>
          </div>
        </div>

        <!-- الهيكل التقني -->
        <div class="section-card technical">
          <h4><i class="bi bi-gear-fill text-danger me-2"></i>الهيكل التقني المطلوب</h4>

          <h6>🗄️ قاعدة البيانات:</h6>
          <div class="database-table">
            <strong>team_wallet_payment_request_logs</strong>
            <div class="code-block">
              Schema::create('team_wallet_payment_request_logs', function (Blueprint $table) { $table->id();
              $table->unsignedBigInteger('team_wallet_id'); $table->unsignedBigInteger('user_id'); // المستخدم الذي طبع
              الطلب $table->unsignedBigInteger('team_id'); // الفريق $table->unsignedBigInteger('team_leader_id'); //
              رئيس الفريق $table->decimal('amount', 10, 2); $table->text('notes')->nullable();
              $table->string('ip_address', 45)->nullable(); $table->timestamp('printed_at'); $table->timestamps(); //
              Foreign Keys $table->foreign('team_wallet_id')->references('id')->on('team_wallet');
              $table->foreign('user_id')->references('id')->on('users');
              $table->foreign('team_id')->references('id')->on('teams');
              $table->foreign('team_leader_id')->references('id')->on('users'); // Indexes
              $table->index(['team_wallet_id', 'printed_at']); $table->index(['team_id', 'printed_at']); });
            </div>
          </div>

          <h6>📊 النماذج المطلوبة:</h6>
          <div class="code-block">
            // app/Models/TeamWalletPaymentRequestLog.php class TeamWalletPaymentRequestLog extends Model { protected
            $fillable = [ 'team_wallet_id', 'user_id', 'team_id', 'team_leader_id', 'amount', 'notes', 'ip_address',
            'printed_at' ]; // العلاقات public function teamWallet() { return $this->belongsTo(Team_Wallet::class); }
            public function user() { return $this->belongsTo(User::class); } public function team() { return
            $this->belongsTo(Teams::class); } public function teamLeader() { return $this->belongsTo(User::class,
            'team_leader_id'); } }
          </div>
        </div>

        <!-- خطة التنفيذ -->
        <div class="section-card implementation">
          <h4><i class="bi bi-list-check text-primary me-2"></i>خطة التنفيذ المفصلة</h4>

          <h6>🎯 المرحلة الأولى: إعداد قاعدة البيانات</h6>
          <div class="step-item">
            <div class="step-number">1</div>
            <h6>تحديث Teams Model</h6>
            <p>إضافة method للحصول على رئيس الفريق من user_has_teams</p>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <h6>إنشاء جدول السجلات</h6>
            <p>إنشاء team_wallet_payment_request_logs مع جميع الحقول المطلوبة</p>
          </div>

          <div class="step-item">
            <div class="step-number">3</div>
            <h6>إنشاء النموذج</h6>
            <p>إنشاء TeamWalletPaymentRequestLog model مع العلاقات</p>
          </div>

          <h6>🎯 المرحلة الثانية: Backend Development</h6>
          <div class="step-item">
            <div class="step-number">4</div>
            <h6>تحديث TeamWalletController</h6>
            <p>إضافة methods للتعامل مع طلبات السحب والسجلات</p>
            <div class="code-block">
              // Methods مطلوبة: - logPaymentRequest() - getPaymentRequestLogs() - generatePaymentPDF() -
              generatePaymentRequestHTML()
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">5</div>
            <h6>إضافة Routes</h6>
            <p>إضافة routes للعمليات الجديدة</p>
            <div class="code-block">
              Route::post('/team-wallets/{wallet}/log-payment-request', [TeamWalletController::class,
              'logPaymentRequest']); Route::get('/team-wallets/{wallet}/payment-request-logs',
              [TeamWalletController::class, 'getPaymentRequestLogs']);
              Route::post('/team-wallets/{wallet}/generate-payment-pdf', [TeamWalletController::class,
              'generatePaymentPDF']);
            </div>
          </div>

          <h6>🎯 المرحلة الثالثة: Frontend Development</h6>
          <div class="step-item">
            <div class="step-number">6</div>
            <h6>تحديث Blade Template</h6>
            <p>إضافة قسم طلب السحب في views/admin/teams/wallets/index.blade.php</p>
          </div>

          <div class="step-item">
            <div class="step-number">7</div>
            <h6>تحديث JavaScript</h6>
            <p>إضافة functions في resources/js/admin/teams/wallet.js</p>
          </div>

          <div class="step-item">
            <div class="step-number">8</div>
            <h6>إنشاء PDF Template</h6>
            <p>إنشاء views/admin/teams/wallets/payment-request-pdf.blade.php</p>
          </div>
        </div>

        <!-- تدفق العمل -->
        <div class="section-card">
          <h4><i class="bi bi-diagram-3 me-2"></i>تدفق العمل المتوقع</h4>

          <div class="workflow-diagram">
            <div class="entity-box">المستخدم</div>
            <span class="arrow">→</span>
            <div class="entity-box">صفحة محفظة الفريق</div>
            <span class="arrow">→</span>
            <div class="entity-box">طلب سحب نقدي</div>
            <br /><br />
            <div class="entity-box">إدخال البيانات</div>
            <span class="arrow">→</span>
            <div class="entity-box">اختيار طريقة الإنشاء</div>
            <span class="arrow">→</span>
            <div class="entity-box">تحميل PDF / طباعة</div>
            <br /><br />
            <div class="entity-box">تسجيل السجل</div>
            <span class="arrow">→</span>
            <div class="entity-box">تحديث قسم السجلات</div>
            <span class="arrow">→</span>
            <div class="entity-box">إشعار المستخدم</div>
          </div>
        </div>

        <!-- الملفات المطلوبة -->
        <div class="section-card">
          <h4><i class="bi bi-files me-2"></i>الملفات المطلوب إنشاؤها/تعديلها</h4>

          <div class="row">
            <div class="col-md-6">
              <h6>📁 ملفات جديدة:</h6>
              <ul class="feature-list">
                <li>database/migrations/xxxx_add_team_leader_to_teams.php</li>
                <li>database/migrations/xxxx_create_team_wallet_payment_request_logs.php</li>
                <li>app/Models/TeamWalletPaymentRequestLog.php</li>
                <li>resources/views/admin/teams/wallets/payment-request-pdf.blade.php</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>✏️ ملفات للتعديل:</h6>
              <ul class="feature-list">
                <li>app/Http/Controllers/admin/TeamWalletController.php</li>
                <li>resources/views/admin/teams/wallets/index.blade.php</li>
                <li>resources/js/admin/teams/wallet.js</li>
                <li>routes/web.php</li>
                <li>app/Models/Teams.php (إضافة علاقة teamLeader)</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- التوصية النهائية -->
        <div class="alert alert-info alert-custom">
          <h5><i class="bi bi-lightbulb me-2"></i>التوصية النهائية:</h5>
          <p class="mb-3">
            بناءً على التحليل، أوصي بـ <strong>الحل الأول</strong>: إضافة حقل team_leader_id إلى جدول teams لأنه:
          </p>
          <ul class="feature-list">
            <li>أكثر وضوحاً ومباشرة</li>
            <li>يسهل الاستعلامات والعلاقات</li>
            <li>يتماشى مع هيكل قاعدة البيانات الحالي</li>
            <li>يوفر مرونة في إدارة رؤساء الفرق</li>
          </ul>
          <p class="mb-0"><strong>هل توافق على هذا التصور وتريد البدء في التنفيذ؟</strong></p>
        </div>
      </div>
    </div>
  </body>
</html>

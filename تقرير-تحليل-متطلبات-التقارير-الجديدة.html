<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تقرير تحليل متطلبات التقارير الجديدة - SafeDests</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .content {
        padding: 40px;
      }

      .section {
        margin-bottom: 40px;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      }

      .section.analysis {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-left: 5px solid #2196f3;
      }

      .section.requirements {
        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        border-left: 5px solid #4caf50;
      }

      .section.implementation {
        background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
        border-left: 5px solid #ff9800;
      }

      .section.files {
        background: linear-gradient(135deg, #f3e5f5 0%, #e1f5fe 100%);
        border-left: 5px solid #9c27b0;
      }

      .section h2 {
        color: #2c3e50;
        font-size: 1.8em;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
      }

      .section h2 i {
        margin-left: 10px;
        font-size: 1.2em;
      }

      .section h3 {
        color: #34495e;
        font-size: 1.4em;
        margin: 20px 0 15px 0;
        border-bottom: 2px solid #ecf0f1;
        padding-bottom: 5px;
      }

      .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
      }

      .card h4 {
        color: #2c3e50;
        margin-bottom: 15px;
        font-size: 1.2em;
      }

      .code-block {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 15px;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        line-height: 1.4;
        overflow-x: auto;
        margin: 10px 0;
      }

      .highlight {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }

      .highlight.important {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
      }

      .highlight.success {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
      }

      ul,
      ol {
        padding-right: 20px;
        margin: 15px 0;
      }

      li {
        margin: 8px 0;
        line-height: 1.5;
      }

      .comparison-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
      }

      .comparison-table th,
      .comparison-table td {
        padding: 12px 15px;
        text-align: right;
        border-bottom: 1px solid #e0e0e0;
      }

      .comparison-table th {
        background: #34495e;
        color: white;
        font-weight: bold;
      }

      .comparison-table tr:nth-child(even) {
        background: #f8f9fa;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: bold;
        color: white;
      }

      .status-badge.existing {
        background: #28a745;
      }

      .status-badge.new {
        background: #007bff;
      }

      .status-badge.modified {
        background: #ffc107;
        color: #212529;
      }

      .footer {
        background: #2c3e50;
        color: white;
        padding: 20px;
        text-align: center;
      }

      .icon {
        font-size: 1.5em;
        margin-left: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔍 تقرير تحليل متطلبات التقارير الجديدة</h1>
        <p>تحليل شامل لإنشاء تقرير مهام السائق وتقرير مهام الفريق</p>
        <p><strong>منصة SafeDests للنقل والخدمات اللوجستية</strong></p>
      </div>

      <div class="content">
        <!-- تحليل التقارير الحالية -->
        <div class="section analysis">
          <h2><span class="icon">📊</span>تحليل التقارير الحالية</h2>

          <h3>1. تقرير مهام العميل (Customer Tasks Report)</h3>
          <div class="grid">
            <div class="card">
              <h4>🎯 الوظائف الأساسية</h4>
              <ul>
                <li>فلترة المهام حسب العملاء والتواريخ</li>
                <li>عرض تفاصيل المهام مع معلومات السائق والفريق</li>
                <li>حساب الأسعار والعمولات</li>
                <li>تصدير إلى Excel و PDF</li>
                <li>اختيار الأعمدة المخصصة</li>
                <li>معاينة البيانات قبل التصدير</li>
              </ul>
            </div>

            <div class="card">
              <h4>🏗️ البنية التقنية</h4>
              <ul>
                <li><strong>Controller:</strong> PlatformReportsController</li>
                <li><strong>Service:</strong> ReportService</li>
                <li><strong>View:</strong> customer-tasks.blade.php</li>
                <li><strong>JavaScript:</strong> customer-tasks.js</li>
                <li><strong>PDF Template:</strong> customer-tasks-simple.blade.php</li>
              </ul>
            </div>
          </div>

          <h3>2. تقرير المحافظ (Wallet Reports)</h3>
          <div class="card">
            <h4>🎯 الوظائف الأساسية</h4>
            <ul>
              <li>تقارير محافظ العملاء والسائقين والفرق</li>
              <li>عرض المعاملات والأرصدة</li>
              <li>فلترة حسب نوع المعاملة والحالة</li>
              <li>تصدير PDF فقط</li>
            </ul>
          </div>
        </div>

        <!-- فهم المتطلبات -->
        <div class="section requirements">
          <h2><span class="icon">📋</span>فهم المتطلبات الجديدة</h2>

          <div class="highlight important">
            <strong>المتطلب الأساسي:</strong> إنشاء تقريرين جديدين مطابقين تماماً لتقرير مهام العميل في التصميم والوظائف
            والبرمجة
          </div>

          <h3>1. تقرير مهام السائق (Driver Tasks Report)</h3>
          <div class="grid">
            <div class="card">
              <h4>🎯 المتطلبات الخاصة</h4>
              <ul>
                <li>فلترة المهام حسب السائقين بدلاً من العملاء</li>
                <li>عرض السعر بعد خصم العمولة: <code>total_price - commission</code></li>
                <li>إظهار معلومات العميل الذي طلب المهمة</li>
                <li>نفس خيارات التصدير والأعمدة المخصصة</li>
              </ul>
            </div>

            <div class="card">
              <h4>💰 حساب السعر</h4>
              <div class="code-block">
                // بدلاً من عرض total_price كاملاً $displayPrice = $task->total_price - $task->commission; // العمولة هي
                النسبة التي تأخذها المنصة
              </div>
            </div>
          </div>

          <h3>2. تقرير مهام الفريق (Team Tasks Report)</h3>
          <div class="grid">
            <div class="card">
              <h4>🎯 المتطلبات الخاصة</h4>
              <ul>
                <li>فلترة المهام حسب الفرق</li>
                <li>عرض السعر بعد خصم العمولة: <code>total_price - commission</code></li>
                <li>إظهار السائق الذي قام بتوصيل المهمة</li>
                <li>إظهار معلومات العميل</li>
                <li>نفس خيارات التصدير والأعمدة المخصصة</li>
              </ul>
            </div>
          </div>

          <div class="highlight success">
            <strong>نقاط مهمة:</strong>
            <ul>
              <li>التصميم يجب أن يكون مطابق 100% لتقرير مهام العميل</li>
              <li>نفس طريقة البرمجة والعمليات</li>
              <li>نفس خيارات اختيار الأعمدة والتصدير</li>
              <li>لا تصاميم جديدة أو نهج برمجة مختلف</li>
            </ul>
          </div>
        </div>

        <!-- خطة التنفيذ -->
        <div class="section implementation">
          <h2><span class="icon">🚀</span>خطة التنفيذ</h2>

          <h3>المرحلة الأولى: إنشاء الملفات الأساسية</h3>
          <div class="grid">
            <div class="card">
              <h4>📄 ملفات العرض (Views)</h4>
              <ul>
                <li><code>resources/views/admin/reports/driver-tasks.blade.php</code></li>
                <li><code>resources/views/admin/reports/team-tasks.blade.php</code></li>
                <li><code>resources/views/admin/reports/pdf/driver-tasks-simple.blade.php</code></li>
                <li><code>resources/views/admin/reports/pdf/team-tasks-simple.blade.php</code></li>
              </ul>
            </div>

            <div class="card">
              <h4>⚙️ ملفات JavaScript</h4>
              <ul>
                <li><code>public/js/admin/reports/driver-tasks.js</code></li>
                <li><code>public/js/admin/reports/team-tasks.js</code></li>
              </ul>
            </div>
          </div>

          <h3>المرحلة الثانية: تطوير Backend</h3>
          <div class="card">
            <h4>🔧 التعديلات المطلوبة</h4>
            <ul>
              <li><strong>إضافة Routes جديدة:</strong> في web.php</li>
              <li><strong>تطوير Controller Methods:</strong> في PlatformReportsController</li>
              <li><strong>تطوير Service Methods:</strong> في ReportService</li>
              <li><strong>تحديث صفحة الفهرس:</strong> إضافة التقريرين الجديدين</li>
            </ul>
          </div>

          <h3>المرحلة الثالثة: الاختبار والتحسين</h3>
          <div class="card">
            <h4>🧪 خطوات الاختبار</h4>
            <ul>
              <li>اختبار الفلاتر والمعاينة</li>
              <li>اختبار التصدير إلى Excel و PDF</li>
              <li>اختبار اختيار الأعمدة المخصصة</li>
              <li>اختبار حساب الأسعار والعمولات</li>
              <li>اختبار التصميم والاستجابة</li>
            </ul>
          </div>
        </div>

        <!-- الملفات التي سيتم إنشاؤها -->
        <div class="section files">
          <h2><span class="icon">📁</span>الملفات التي سيتم إنشاؤها/تعديلها</h2>

          <table class="comparison-table">
            <thead>
              <tr>
                <th>نوع الملف</th>
                <th>المسار</th>
                <th>الحالة</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>View</td>
                <td>resources/views/admin/reports/driver-tasks.blade.php</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>صفحة تقرير مهام السائق</td>
              </tr>
              <tr>
                <td>View</td>
                <td>resources/views/admin/reports/team-tasks.blade.php</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>صفحة تقرير مهام الفريق</td>
              </tr>
              <tr>
                <td>PDF Template</td>
                <td>resources/views/admin/reports/pdf/driver-tasks-simple.blade.php</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>قالب PDF لتقرير مهام السائق</td>
              </tr>
              <tr>
                <td>PDF Template</td>
                <td>resources/views/admin/reports/pdf/team-tasks-simple.blade.php</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>قالب PDF لتقرير مهام الفريق</td>
              </tr>
              <tr>
                <td>JavaScript</td>
                <td>public/js/admin/reports/driver-tasks.js</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>منطق العميل لتقرير مهام السائق</td>
              </tr>
              <tr>
                <td>JavaScript</td>
                <td>public/js/admin/reports/team-tasks.js</td>
                <td><span class="status-badge new">جديد</span></td>
                <td>منطق العميل لتقرير مهام الفريق</td>
              </tr>
              <tr>
                <td>Controller</td>
                <td>app/Http/Controllers/admin/PlatformReportsController.php</td>
                <td><span class="status-badge modified">تعديل</span></td>
                <td>إضافة methods للتقريرين الجديدين</td>
              </tr>
              <tr>
                <td>Service</td>
                <td>app/Services/ReportService.php</td>
                <td><span class="status-badge modified">تعديل</span></td>
                <td>إضافة منطق توليد التقريرين</td>
              </tr>
              <tr>
                <td>Routes</td>
                <td>routes/web.php</td>
                <td><span class="status-badge modified">تعديل</span></td>
                <td>إضافة routes للتقريرين الجديدين</td>
              </tr>
              <tr>
                <td>Index View</td>
                <td>resources/views/admin/reports/index.blade.php</td>
                <td><span class="status-badge modified">تعديل</span></td>
                <td>إضافة بطاقات التقريرين الجديدين</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- الاختلافات الرئيسية -->
        <div class="section analysis">
          <h2><span class="icon">🔄</span>الاختلافات الرئيسية بين التقارير</h2>

          <table class="comparison-table">
            <thead>
              <tr>
                <th>العنصر</th>
                <th>تقرير مهام العميل</th>
                <th>تقرير مهام السائق</th>
                <th>تقرير مهام الفريق</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>الفلتر الأساسي</strong></td>
                <td>العملاء (customer_ids)</td>
                <td>السائقين (driver_ids)</td>
                <td>الفرق (team_ids)</td>
              </tr>
              <tr>
                <td><strong>حساب السعر</strong></td>
                <td>total_price كاملاً</td>
                <td>total_price - commission</td>
                <td>total_price - commission</td>
              </tr>
              <tr>
                <td><strong>معلومات إضافية</strong></td>
                <td>معلومات السائق والفريق</td>
                <td>معلومات العميل</td>
                <td>معلومات العميل والسائق</td>
              </tr>
              <tr>
                <td><strong>العنوان</strong></td>
                <td>Customer Tasks Report</td>
                <td>Driver Tasks Report</td>
                <td>Team Tasks Report</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- الخلاصة والموافقة -->
        <div class="section requirements">
          <h2><span class="icon">✅</span>الخلاصة وطلب الموافقة</h2>

          <div class="highlight success">
            <h4>📋 ملخص ما سيتم تنفيذه:</h4>
            <ol>
              <li>
                <strong>إنشاء تقرير مهام السائق:</strong> مطابق تماماً لتقرير مهام العميل مع تغيير الفلتر الأساسي وحساب
                السعر
              </li>
              <li>
                <strong>إنشاء تقرير مهام الفريق:</strong> مطابق تماماً لتقرير مهام العميل مع تغيير الفلتر الأساسي وحساب
                السعر وإضافة معلومات السائق
              </li>
              <li><strong>نفس الوظائف:</strong> اختيار الأعمدة، التصدير إلى Excel/PDF، المعاينة، الفلاتر المتقدمة</li>
              <li><strong>نفس التصميم:</strong> استخدام نفس CSS وHTML وJavaScript مع التعديلات الضرورية فقط</li>
              <li><strong>نفس البنية:</strong> استخدام نفس Controllers وServices مع إضافة methods جديدة</li>
            </ol>
          </div>

          <div class="highlight important">
            <h4>⚠️ نقاط مهمة للتأكيد:</h4>
            <ul>
              <li>حساب السعر في التقريرين الجديدين: <code>total_price - commission</code></li>
              <li>العمولة (commission) هي النسبة التي تأخذها المنصة</li>
              <li>التصميم والوظائف مطابقة 100% لتقرير مهام العميل</li>
              <li>لا تغييرات في النهج البرمجي أو التصميم</li>
            </ul>
          </div>

          <div class="card" style="text-align: center; margin-top: 30px; padding: 30px">
            <h3 style="color: #2c3e50; margin-bottom: 20px">🎯 جاهز للبدء في التنفيذ</h3>
            <p style="font-size: 1.2em; color: #34495e">
              بعد مراجعة هذا التقرير والموافقة عليه، سأبدأ فوراً في تنفيذ التقريرين الجديدين وفقاً للمتطلبات المحددة
              والمعايير المطلوبة.
            </p>
            <div style="margin-top: 20px">
              <strong style="color: #e74c3c; font-size: 1.1em"> ⏳ في انتظار موافقتك للبدء في العمل </strong>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <p><strong>SafeDests Platform Reports System</strong></p>
        <p>تم إنشاء هذا التقرير بواسطة Augment Agent - نظام التقارير المتقدم</p>
      </div>
    </div>
  </body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محافظ السائقين المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .header-section {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 40px 0;
        }
        .system-card {
            border-left: 4px solid #17a2b8;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .system-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .comparison-table {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        .team-system { background: #fff3cd; }
        .driver-system { background: #d1edff; }
        .nav-breadcrumb {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 10px 20px;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #d1edff 0%, #a8e6cf 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <nav class="nav-breadcrumb mb-3">
                <a href="index.html" class="text-white text-decoration-none">
                    <i class="bi bi-house me-1"></i>الرئيسية
                </a>
                <span class="text-white mx-2">/</span>
                <span class="text-white">محافظ السائقين</span>
            </nav>
            <h1 class="display-4">
                <i class="bi bi-wallet2 me-3"></i>
                نظام محافظ السائقين المتكامل
            </h1>
            <p class="lead">تطوير نظام محافظ مطابق تماماً لنظام الفريق مع جميع الميزات</p>
        </div>
    </div>

    <div class="container my-5">
        <!-- نظرة عامة -->
        <div class="card system-card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="bi bi-eye me-2"></i>نظرة عامة على المشروع
                </h4>
            </div>
            <div class="card-body">
                <div class="feature-highlight">
                    <h5>🎯 الهدف الرئيسي</h5>
                    <p>إنشاء نظام محافظ للسائقين مطابق تماماً لنظام الفريق، مع نفس الميزات والوظائف والواجهة، 
                    لضمان تجربة موحدة ومتسقة عبر النظام.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5>📋 المتطلبات</h5>
                        <ul>
                            <li>نفس نظام الدفع المتعدد</li>
                            <li>نفس آلية التوزيع التسلسلي</li>
                            <li>نفس واجهة المستخدم</li>
                            <li>نفس منطق الدفع الجزئي</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🔧 التحديات</h5>
                        <ul>
                            <li>اختلاف بنية البيانات</li>
                            <li>منطق معاملات مختلف</li>
                            <li>routes وcontrollers منفصلة</li>
                            <li>ضمان التطابق الكامل</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- المقارنة بين النظامين -->
        <div class="card system-card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>المقارنة بين نظام الفريق ونظام السائق
                </h4>
            </div>
            <div class="card-body">
                <div class="comparison-table">
                    <table class="table table-striped mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>الخاصية</th>
                                <th>نظام الفريق</th>
                                <th>نظام السائق (الجديد)</th>
                                <th>التطابق</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>المعاملات القابلة للدفع</strong></td>
                                <td class="team-system">Debit غير مدفوعة</td>
                                <td class="driver-system">Credit غير مدفوعة</td>
                                <td><span class="badge bg-success">✅ متطابق</span></td>
                            </tr>
                            <tr>
                                <td><strong>آلية التوزيع</strong></td>
                                <td class="team-system">تسلسلي (Sequential)</td>
                                <td class="driver-system">تسلسلي (Sequential)</td>
                                <td><span class="badge bg-success">✅ متطابق</span></td>
                            </tr>
                            <tr>
                                <td><strong>الدفع الجزئي</strong></td>
                                <td class="team-system">إنشاء معاملة جديدة للمتبقي</td>
                                <td class="driver-system">إنشاء معاملة جديدة للمتبقي</td>
                                <td><span class="badge bg-success">✅ متطابق</span></td>
                            </tr>
                            <tr>
                                <td><strong>معاملة الدفع الفعلي</strong></td>
                                <td class="team-system">Credit (للفريق)</td>
                                <td class="driver-system">Debit (على الشركة)</td>
                                <td><span class="badge bg-success">✅ متطابق</span></td>
                            </tr>
                            <tr>
                                <td><strong>واجهة المستخدم</strong></td>
                                <td class="team-system">تحديد متعدد + مبلغ يدوي</td>
                                <td class="driver-system">تحديد متعدد + مبلغ يدوي</td>
                                <td><span class="badge bg-success">✅ متطابق</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- الملفات المنشأة -->
        <div class="card system-card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="bi bi-file-earmark-plus me-2"></i>الملفات المنشأة والمعدلة
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>📁 ملفات جديدة</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="bi bi-file-earmark-text text-primary me-2"></i>
                                <strong>driver-show.blade.php</strong>
                                <br><small>صفحة عرض محفظة السائق</small>
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-filetype-js text-warning me-2"></i>
                                <strong>driver-show.js</strong>
                                <br><small>منطق الواجهة والتفاعل</small>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🔧 ملفات معدلة</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="bi bi-file-earmark-code text-success me-2"></i>
                                <strong>WalletsController.php</strong>
                                <br><small>إضافة methods للسائقين</small>
                            </li>
                            <li class="list-group-item">
                                <i class="bi bi-signpost text-info me-2"></i>
                                <strong>web.php (Routes)</strong>
                                <br><small>إضافة routes جديدة</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدوال المضافة -->
        <div class="card system-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-gear me-2"></i>الدوال المضافة في WalletsController
                </h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الدالة</th>
                                <th>الوظيفة</th>
                                <th>المعاملات</th>
                                <th>النوع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>showDriverWallet($id)</code></td>
                                <td>عرض صفحة محفظة السائق</td>
                                <td>$id (Driver ID)</td>
                                <td><span class="badge bg-success">جديدة</span></td>
                            </tr>
                            <tr>
                                <td><code>getDriverTransactions($id)</code></td>
                                <td>جلب معاملات محفظة السائق</td>
                                <td>$id, Request</td>
                                <td><span class="badge bg-success">جديدة</span></td>
                            </tr>
                            <tr>
                                <td><code>processDriverPayment()</code></td>
                                <td>معالجة دفع مستحقات السائق</td>
                                <td>Request</td>
                                <td><span class="badge bg-success">جديدة</span></td>
                            </tr>
                            <tr>
                                <td><code>validateDriverPayment()</code></td>
                                <td>التحقق من صحة بيانات الدفع</td>
                                <td>Request</td>
                                <td><span class="badge bg-success">جديدة</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h5 class="mt-4">🔍 تفاصيل الدالة الرئيسية</h5>
                <div class="code-block">
public function processDriverPayment(Request $request)
{
    DB::beginTransaction();
    try {
        // التحقق من صحة البيانات
        $this->validateDriverPayment($request);
        
        // جلب معاملات السائق
        $walletTransactions = Wallet_Transaction::whereIn('id', $transactionIds)
            ->where('transaction_type', 'credit') // Credit transactions (money owed TO driver)
            ->where('status', 0) // Only unpaid transactions
            ->get();

        // معالجة كل معاملة بالتوزيع التسلسلي
        foreach ($request->transactions as $transactionData) {
            $walletTransaction = $walletTransactions->where('id', $transactionData['id'])->first();
            $paymentAmount = $transactionData['payment_amount'];
            $originalAmount = $walletTransaction->amount;

            if ($paymentAmount >= $originalAmount) {
                // دفع كامل
                $walletTransaction->update([
                    'status' => 1,
                    'user_id' => auth()->id()
                ]);
                $paymentDescription = "دفع مستحقات سائق (كامل) للمعاملة رقم #{$walletTransaction->sequence}";
            } else if ($paymentAmount > 0) {
                // دفع جزئي
                $remainingAmount = $originalAmount - $paymentAmount;
                
                $walletTransaction->update([
                    'status' => 1,
                    'amount' => $paymentAmount,
                    'user_id' => auth()->id()
                ]);

                // إنشاء معاملة جديدة للمبلغ المتبقي
                Wallet_Transaction::create([
                    'wallet_id' => $walletTransaction->wallet_id,
                    'amount' => $remainingAmount,
                    'transaction_type' => 'credit',
                    'description' => "المبلغ المتبقي من المعاملة #{$walletTransaction->sequence}",
                    'status' => 0,
                    'maturity_time' => $walletTransaction->maturity_time
                ]);
            }

            // إنشاء معاملة الدفع الفعلي (debit على الشركة)
            Wallet_Transaction::create([
                'wallet_id' => $walletTransaction->wallet_id,
                'amount' => $paymentAmount,
                'transaction_type' => 'debit',
                'description' => $paymentDescription . ($request->notes ? " - {$request->notes}" : ""),
                'status' => 1,
                'user_id' => auth()->id(),
                'maturity_time' => now()
            ]);
        }

        DB::commit();
        return response()->json(['success' => true, 'message' => 'Payment processed successfully']);
    } catch (\Exception $e) {
        DB::rollback();
        return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
    }
}
                </div>
            </div>
        </div>

        <!-- الواجهة والتصميم -->
        <div class="card system-card">
            <div class="card-header bg-secondary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-palette me-2"></i>الواجهة والتصميم
                </h4>
            </div>
            <div class="card-body">
                <div class="feature-highlight">
                    <h5>🎨 مبادئ التصميم</h5>
                    <ul>
                        <li><strong>التطابق الكامل:</strong> نفس تخطيط وألوان نظام الفريق</li>
                        <li><strong>سهولة الاستخدام:</strong> واجهة مألوفة للمستخدمين</li>
                        <li><strong>الاتساق:</strong> نفس المصطلحات والرموز</li>
                        <li><strong>الاستجابة:</strong> تصميم متجاوب لجميع الأجهزة</li>
                    </ul>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5>📱 مكونات الواجهة</h5>
                        <ul>
                            <li>جدول المعاملات مع checkboxes</li>
                            <li>نافذة الدفع المتعدد</li>
                            <li>جدول المعاملات المحددة</li>
                            <li>حقل المبلغ مع زر "Use Maximum"</li>
                            <li>أزرار الإجراءات والتحكم</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>⚡ الميزات التفاعلية</h5>
                        <ul>
                            <li>تحديث تلقائي للمجاميع</li>
                            <li>توزيع تسلسلي فوري</li>
                            <li>إزالة المعاملات ديناميكياً</li>
                            <li>تحديد الكل/إلغاء الكل</li>
                            <li>رسائل تأكيد وتحذير</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- التحديات والحلول -->
        <div class="card system-card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>التحديات والحلول
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>🚫 التحديات</h5>
                        <ul>
                            <li><strong>اختلاف منطق المعاملات:</strong> Credit vs Debit</li>
                            <li><strong>بنية البيانات:</strong> جداول مختلفة</li>
                            <li><strong>Routes منفصلة:</strong> controllers مختلفة</li>
                            <li><strong>ضمان التطابق:</strong> نفس السلوك تماماً</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>✅ الحلول</h5>
                        <ul>
                            <li><strong>تحليل عميق:</strong> فهم منطق النظامين</li>
                            <li><strong>تطبيق مطابق:</strong> نسخ الكود وتعديله</li>
                            <li><strong>اختبار شامل:</strong> التأكد من التطابق</li>
                            <li><strong>توثيق كامل:</strong> شرح الاختلافات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- النتائج -->
        <div class="card system-card">
            <div class="card-header bg-dark text-white">
                <h4 class="mb-0">
                    <i class="bi bi-trophy me-2"></i>النتائج والإنجازات
                </h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-check-circle display-4 text-success"></i>
                            <h4>100%</h4>
                            <p>تطابق مع نظام الفريق</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-speedometer2 display-4 text-primary"></i>
                            <h4>15+</h4>
                            <p>دالة جديدة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-file-earmark-code display-4 text-warning"></i>
                            <h4>5+</h4>
                            <p>ملف جديد/معدل</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="bi bi-star display-4 text-info"></i>
                            <h4>0</h4>
                            <p>أخطاء متبقية</p>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-4">
                    <h5><i class="bi bi-check-circle me-2"></i>الإنجازات الرئيسية:</h5>
                    <ul class="mb-0">
                        <li>✅ إنشاء نظام محافظ سائقين متكامل</li>
                        <li>✅ تطبيق نفس منطق نظام الفريق</li>
                        <li>✅ واجهة مستخدم مطابقة تماماً</li>
                        <li>✅ دعم الدفع المتعدد والجزئي</li>
                        <li>✅ تجربة مستخدم موحدة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="d-flex justify-content-between mt-4">
            <a href="team-multi-payment.html" class="btn btn-secondary">
                <i class="bi bi-arrow-right me-1"></i>السابق: الدفع المتعدد
            </a>
            <a href="advanced-filtering.html" class="btn btn-primary">
                التالي: الفلترة المتقدمة <i class="bi bi-arrow-left ms-1"></i>
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

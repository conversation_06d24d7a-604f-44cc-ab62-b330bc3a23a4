<!doctype html>
<html dir="rtl" lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>اختبار طلب السداد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="resources/css/payment-request-print.css" />
  </head>
  <body>
    <div class="container mt-5">
      <h2 class="text-center mb-4">اختبار نظام طلب السداد</h2>

      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5>معلومات المهمة</h5>
            </div>
            <div class="card-body">
              <p><strong>رقم المهمة:</strong> #12345</p>
              <p><strong>المبلغ الإجمالي:</strong> 1000.00 ريال</p>
              <p><strong>العمولة:</strong> 150.00 ريال</p>
              <p><strong>مبلغ السائق:</strong> 850.00 ريال</p>
              <p><strong>صاحب المهمة:</strong> أحمد محمد</p>
              <p><strong>نقطة الاستلام:</strong> الرياض - حي النخيل</p>
              <p><strong>نقطة التسليم:</strong> جدة - حي الصفا</p>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5>نموذج طلب السداد</h5>
            </div>
            <div class="card-body">
              <form id="testForm">
                <div class="mb-3">
                  <label class="form-label">المبلغ المطلوب</label>
                  <input type="number" class="form-control" id="testAmount" value="500" step="0.01" />
                </div>
                <div class="mb-3">
                  <label class="form-label">اسم البنك</label>
                  <input type="text" class="form-control" id="testBank" value="البنك الأهلي السعودي" />
                </div>
                <div class="mb-3">
                  <label class="form-label">رقم الحساب</label>
                  <input type="text" class="form-control" id="testAccount" value="**********" />
                </div>
                <div class="mb-3">
                  <label class="form-label">رقم الآيبان</label>
                  <input type="text" class="form-control" id="testIban" value="SA12 3456 7890 1234 5678 90" />
                </div>
                <div class="mb-3">
                  <label class="form-label">المستفيد</label>
                  <select class="form-select" id="testRecipient">
                    <option value="driver">السائق</option>
                    <option value="team_leader">رئيس الفريق</option>
                  </select>
                </div>
                <button type="button" class="btn btn-primary" onclick="testPrint()">طباعة طلب السداد</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      function testPrint() {
        const data = {
          taskId: 12345,
          requestedAmount: parseFloat(document.getElementById('testAmount').value),
          bankName: document.getElementById('testBank').value,
          accountNumber: document.getElementById('testAccount').value,
          ibanNumber: document.getElementById('testIban').value,
          paymentRecipient: document.getElementById('testRecipient').value,
          taskData: {
            id: 12345,
            total_price: 1000,
            commission: 150,
            driver_amount: 850,
            driver_name: 'محمد أحمد',
            team_leader_name: 'علي حسن',
            owner_name: 'أحمد محمد',
            pickup_address: 'الرياض - حي النخيل',
            delivery_address: 'جدة - حي الصفا'
          }
        };

        const today = new Date();
        const formattedDate = today.toLocaleDateString('ar-SA');
        const remainingAmount = data.taskData.driver_amount - data.requestedAmount;
        const recipientName =
          data.paymentRecipient === 'driver' ? data.taskData.driver_name : data.taskData.team_leader_name;
        const requestedAmountInWords = numberToArabicWords(data.requestedAmount);

        const printContent = `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>طلب سداد - مهمة #${data.taskId}</title>
                    <link rel="stylesheet" href="resources/css/payment-request-print.css">
                </head>
                <body class="payment-request-print">
                    <div class="payment-request-container">
                        <!-- Company Letterhead -->
                        <div class="payment-request-letterhead">
                            <div class="payment-request-company-name">شركة النقل الآمن</div>
                            <div class="payment-request-company-subtitle">خدمات النقل والتوصيل</div>
                            <div class="payment-request-document-title">طلب سداد</div>
                            <div class="payment-request-document-number">رقم الطلب: PR-${data.taskId}-${Date.now()}</div>
                        </div>

                        <!-- Date Section -->
                        <div class="payment-request-date-section">
                            التاريخ: ${formattedDate}
                        </div>

                        <!-- Employee Information -->
                        <div class="payment-request-section">
                            <table class="payment-request-table">
                                <tr>
                                    <td class="label-cell">اسم الموظف طالب السداد</td>
                                    <td class="value-cell">مدير النظام</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Payment Amount Section -->
                        <div class="payment-request-section">
                            <div class="payment-request-section-title">بيانات السداد</div>
                            <div class="payment-request-amount-section">
                                <div class="payment-request-amount-title">مبلغ السداد</div>
                                <div class="payment-request-amount-words">${requestedAmountInWords}</div>
                            </div>
                        </div>

                        <!-- Payment Breakdown -->
                        <div class="payment-request-section">
                            <div class="payment-request-section-title">السداد</div>
                            <table class="payment-request-table">
                                <tr>
                                    <td class="label-cell">دفعة</td>
                                    <td class="value-cell">${data.requestedAmount.toFixed(2)} ريال</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">باقي حساب</td>
                                    <td class="value-cell">${remainingAmount.toFixed(2)} ريال</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">الحساب كامل</td>
                                    <td class="value-cell">${data.taskData.driver_amount.toFixed(2)} ريال</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Bank Information -->
                        <div class="payment-request-section">
                            <div class="payment-request-section-title">بيانات البنك</div>
                            <table class="payment-request-table">
                                <tr>
                                    <td class="label-cell">اسم البنك</td>
                                    <td class="value-cell">${data.bankName}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">رقم الحساب</td>
                                    <td class="value-cell">${data.accountNumber}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">رقم الآيبان</td>
                                    <td class="value-cell">${data.ibanNumber}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">اسم المورد</td>
                                    <td class="value-cell">${recipientName}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Trip Information -->
                        <div class="payment-request-section">
                            <div class="payment-request-section-title">بيانات الرحلة</div>
                            <table class="payment-request-table">
                                <tr>
                                    <td class="label-cell">رقم المهمة</td>
                                    <td class="value-cell">#${data.taskId}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">اسم صاحب الرحلة</td>
                                    <td class="value-cell">${data.taskData.owner_name}</td>
                                </tr>
                                <tr>
                                    <td class="label-cell">جهة الرحلة</td>
                                    <td class="value-cell">من ${data.taskData.pickup_address}<br>إلى ${data.taskData.delivery_address}</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Signature Section -->
                        <div class="payment-request-signature-section">
                            <table class="payment-request-signature-table">
                                <tr>
                                    <td>إعداد الطلب<br><br><br>_______________</td>
                                    <td>مراجعة المحاسب<br><br><br>_______________</td>
                                    <td>اعتماد المدير<br><br><br>_______________</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Footer -->
                        <div class="payment-request-footer">
                            <p>هذا المستند تم إنشاؤه إلكترونياً في ${new Date().toLocaleString('ar-SA')}</p>
                        </div>
                    </div>
                </body>
                </html>
            `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }

      function numberToArabicWords(num) {
        const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
        const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
        const teens = [
          'عشرة',
          'أحد عشر',
          'اثنا عشر',
          'ثلاثة عشر',
          'أربعة عشر',
          'خمسة عشر',
          'ستة عشر',
          'سبعة عشر',
          'ثمانية عشر',
          'تسعة عشر'
        ];
        const hundreds = [
          '',
          'مائة',
          'مائتان',
          'ثلاثمائة',
          'أربعمائة',
          'خمسمائة',
          'ستمائة',
          'سبعمائة',
          'ثمانمائة',
          'تسعمائة'
        ];

        if (num === 0) return 'صفر ريال';

        let result = '';
        const integerPart = Math.floor(num);
        const decimalPart = Math.round((num - integerPart) * 100);

        if (integerPart >= 1000) {
          const thousands = Math.floor(integerPart / 1000);
          result += convertHundreds(thousands) + ' ألف ';
          const remainder = integerPart % 1000;
          if (remainder > 0) {
            result += convertHundreds(remainder);
          }
        } else {
          result = convertHundreds(integerPart);
        }

        result += ' ريال';

        if (decimalPart > 0) {
          result += ' و ' + convertHundreds(decimalPart) + ' هللة';
        }

        return result.trim();

        function convertHundreds(num) {
          let result = '';

          if (num >= 100) {
            const hundredsDigit = Math.floor(num / 100);
            result += hundreds[hundredsDigit] + ' ';
            num %= 100;
          }

          if (num >= 20) {
            const tensDigit = Math.floor(num / 10);
            result += tens[tensDigit] + ' ';
            num %= 10;
            if (num > 0) {
              result += ones[num] + ' ';
            }
          } else if (num >= 10) {
            result += teens[num - 10] + ' ';
          } else if (num > 0) {
            result += ones[num] + ' ';
          }

          return result.trim();
        }
      }
    </script>
  </body>
</html>

<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Team Management System Catalog - SafeDest</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
      }
      .container {
        max-width: 1200px;
      }
      .main-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        text-align: center;
      }
      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
      }
      .nav-tabs {
        border-bottom: 3px solid #667eea;
        background: #f8f9fa;
        padding: 0 20px;
      }
      .nav-tabs .nav-link {
        border: none;
        color: #666;
        font-weight: 600;
        padding: 15px 25px;
        margin: 0 5px;
        border-radius: 10px 10px 0 0;
      }
      .nav-tabs .nav-link.active {
        background: #667eea;
        color: white;
      }
      .tab-content {
        padding: 30px;
      }
      .section-card {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 5px solid #667eea;
      }
      .section-title {
        color: #667eea;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 15px;
      }
      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 10px;
        margin: 15px 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
      }
      .route-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #28a745;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }
      .method-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 5px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-left: 10px;
      }
      .get {
        background: #28a745;
        color: white;
      }
      .post {
        background: #007bff;
        color: white;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }
      .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-top: 4px solid #667eea;
      }
      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
      }
      .feature-list {
        list-style: none;
        padding: 0;
      }
      .feature-list li {
        background: white;
        margin: 8px 0;
        padding: 12px 15px;
        border-radius: 8px;
        border-right: 4px solid #28a745;
      }
      .feature-list li:before {
        content: '✅';
        margin-left: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main-card">
        <div class="header">
          <h1><i class="fas fa-users-cog"></i> Team Management System Catalog</h1>
          <p>Complete Guide for Functions, Views, and Routes - SafeDest Platform</p>
        </div>

        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
          <li class="nav-item">
            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#overview">Overview</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#database">Database</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#routes">Routes</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#controllers">Controllers</button>
          </li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#models">Models</button>
          </li>
          <li class="nav-item"><button class="nav-link" data-bs-toggle="tab" data-bs-target="#views">Views</button></li>
          <li class="nav-item">
            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#javascript">JavaScript</button>
          </li>
        </ul>

        <div class="tab-content">
          <!-- Overview Tab -->
          <div class="tab-pane fade show active" id="overview">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">3</div>
                <div>Controllers</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">2</div>
                <div>Models</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">25+</div>
                <div>Routes</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">5</div>
                <div>Views</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">5</div>
                <div>JS Files</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">3</div>
                <div>Migrations</div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">Key Features Implemented</h3>
              <ul class="feature-list">
                <li>Advanced Driver Management with Status Updates</li>
                <li>Smart Task Assignment Based on Criteria</li>
                <li>Automatic Task Assignment Algorithm</li>
                <li>Comprehensive Performance Reports with Charts</li>
                <li>Team Task Management with Advanced Filtering</li>
                <li>Unified Team Management Dashboard</li>
                <li>Advanced Performance Evaluation System</li>
                <li>Multi-format Report Export (Excel, PDF, CSV)</li>
              </ul>
            </div>

            <div class="section-card">
              <h3 class="section-title">Project Structure</h3>
              <div class="code-block">
                app/Http/Controllers/admin/ ├── TeamManagementController.php ├── TeamTaskAssignmentController.php └──
                TeamPerformanceController.php app/Models/ ├── TeamTaskAssignment.php └── TeamPerformanceLog.php
                resources/views/admin/teams/ ├── management/ (index.blade.php, drivers.blade.php, tasks.blade.php) ├──
                assignment/ (index.blade.php) └── performance/ (index.blade.php) resources/assets/js/admin/teams/ ├──
                management.js, drivers.js, assignment.js ├── tasks.js, performance.js database/migrations/ ├──
                create_team_task_assignments_table.php ├── create_team_performance_logs_table.php └──
                add_team_management_fields_to_existing_tables.php
              </div>
            </div>
          </div>

          <!-- Database Tab -->
          <div class="tab-pane fade" id="database">
            <div class="section-card">
              <h3 class="section-title">New Tables</h3>
              <div class="row">
                <div class="col-md-6">
                  <h5>team_task_assignments</h5>
                  <div class="code-block">
                    id (bigint, primary key) team_id (bigint, FK → teams.id) task_id (bigint, FK → tasks.id) assigned_by
                    (bigint, FK → users.id) assigned_at (timestamp) status (varchar) - pending, accepted, rejected,
                    completed notes (text, nullable) created_at, updated_at (timestamps)
                  </div>
                </div>
                <div class="col-md-6">
                  <h5>team_performance_logs</h5>
                  <div class="code-block">
                    id (bigint, primary key) team_id (bigint, FK → teams.id) driver_id (bigint, FK → drivers.id) task_id
                    (bigint, FK → tasks.id) performance_score (decimal 5,2) completion_time_minutes (integer)
                    distance_traveled (decimal 8,2) completion_status (varchar) performance_metrics (jsonb) notes (text,
                    nullable) created_at, updated_at (timestamps)
                  </div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">Added Fields to Existing Tables</h3>
              <div class="row">
                <div class="col-md-4">
                  <h5>teams</h5>
                  <div class="code-block">
                    auto_assignment_enabled (boolean) max_concurrent_tasks (integer) team_status (varchar)
                    assignment_criteria (jsonb)
                  </div>
                </div>
                <div class="col-md-4">
                  <h5>drivers</h5>
                  <div class="code-block">
                    availability_status (varchar) last_activity_at (timestamp) max_concurrent_tasks (integer)
                    performance_metrics (jsonb)
                  </div>
                </div>
                <div class="col-md-4">
                  <h5>tasks</h5>
                  <div class="code-block">assigned_by_team (bigint) team_assigned_at (timestamp)</div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">New Relationships</h3>
              <ul class="feature-list">
                <li><strong>Teams → TeamTaskAssignment:</strong> hasMany</li>
                <li><strong>Teams → TeamPerformanceLog:</strong> hasMany</li>
                <li><strong>Teams → Tasks (assigned):</strong> hasManyThrough</li>
                <li><strong>Driver → TeamPerformanceLog:</strong> hasMany</li>
                <li><strong>Task → TeamTaskAssignment:</strong> hasOne</li>
                <li><strong>Task → TeamPerformanceLog:</strong> hasMany</li>
              </ul>
            </div>
          </div>

          <!-- Routes Tab -->
          <div class="tab-pane fade" id="routes">
            <div class="section-card">
              <h3 class="section-title">Team Management Routes</h3>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/management</strong>
                <br /><small>teams.management.index → TeamManagementController@index</small>
                <p>Main team management dashboard with statistics and activities</p>
              </div>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/management/drivers</strong>
                <br /><small>teams.management.drivers → TeamManagementController@drivers</small>
                <p>Advanced driver management page with interactive table</p>
              </div>
              <div class="route-item">
                <span class="method-badge post">POST</span>
                <strong>/admin/teams/{teamId}/management/drivers/{driverId}/status</strong>
                <br /><small>teams.management.drivers.status → TeamManagementController@updateDriverStatus</small>
                <p>Update driver status (available/busy/offline)</p>
              </div>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/management/tasks</strong>
                <br /><small>teams.management.tasks → TeamManagementController@tasks</small>
                <p>Team tasks management page</p>
              </div>
              <div class="route-item">
                <span class="method-badge post">POST</span>
                <strong>/admin/teams/{teamId}/management/settings</strong>
                <br /><small>teams.management.settings → TeamManagementController@updateSettings</small>
                <p>Update team settings (auto assignment, max tasks)</p>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">Task Assignment Routes</h3>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/assignment</strong>
                <br /><small>teams.assignment.index → TeamTaskAssignmentController@index</small>
                <p>Smart task assignment page</p>
              </div>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/assignment/available-tasks</strong>
                <br /><small>teams.assignment.available-tasks → TeamTaskAssignmentController@getAvailableTasks</small>
                <p>API to fetch available tasks for assignment with filtering</p>
              </div>
              <div class="route-item">
                <span class="method-badge post">POST</span>
                <strong>/admin/teams/{teamId}/assignment/assign-task</strong>
                <br /><small>teams.assignment.assign-task → TeamTaskAssignmentController@assignTask</small>
                <p>Assign single task to specific driver</p>
              </div>
              <div class="route-item">
                <span class="method-badge post">POST</span>
                <strong>/admin/teams/{teamId}/assignment/auto-assign</strong>
                <br /><small>teams.assignment.auto-assign → TeamTaskAssignmentController@autoAssign</small>
                <p>Smart automatic task assignment</p>
              </div>
            </div>

            <div class="section-card">
              <h3 class="section-title">Performance Reports Routes</h3>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/performance</strong>
                <br /><small>teams.performance.index → TeamPerformanceController@index</small>
                <p>Comprehensive performance reports page</p>
              </div>
              <div class="route-item">
                <span class="method-badge get">GET</span>
                <strong>/admin/teams/{teamId}/performance/export</strong>
                <br /><small>teams.performance.export → TeamPerformanceController@exportReport</small>
                <p>Export performance reports (Excel, PDF, CSV)</p>
              </div>
            </div>
          </div>

                <!-- Controllers Tab -->
                <div class="tab-pane fade" id="controllers">
                    <div class="section-card">
                        <h3 class="section-title">TeamManagementController</h3>
                        <div class="code-block">
index($teamId) - Main dashboard with statistics
drivers($teamId) - Driver management page
getDriversData($teamId) - API for drivers table
updateDriverStatus($teamId, $driverId) - Update driver status
tasks($teamId) - Team tasks page
getTasksData($teamId) - API for tasks table
updateSettings($teamId) - Update team settings
performance($teamId) - Performance reports page
                        </div>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">TeamTaskAssignmentController</h3>
                        <div class="code-block">
index($teamId) - Task assignment page
getAvailableTasks($teamId) - Get available tasks with filtering
assignTask($teamId) - Assign single task to driver
bulkAssign($teamId) - Bulk task assignment
autoAssign($teamId) - Smart automatic assignment
getAvailableDrivers($teamId) - Get available drivers
performAutoAssignment($team) - Auto assignment algorithm
                        </div>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">TeamPerformanceController</h3>
                        <div class="code-block">
index($teamId) - Performance reports dashboard
getDriversPerformance($teamId) - Drivers performance data
getPerformanceLogsData($teamId) - Performance logs data
exportReport($teamId) - Export reports (Excel, PDF, CSV)
                        </div>
                    </div>
                </div>

                <!-- Models Tab -->
                <div class="tab-pane fade" id="models">
                    <div class="section-card">
                        <h3 class="section-title">TeamTaskAssignment Model</h3>
                        <div class="code-block">
Relationships:
- belongsTo(Teams::class, 'team_id')
- belongsTo(Task::class, 'task_id')
- belongsTo(User::class, 'assigned_by')

Methods:
- getStatusLabelAttribute() - Get status label
- scopeByTeam($query, $teamId) - Filter by team
- scopeByStatus($query, $status) - Filter by status
                        </div>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">TeamPerformanceLog Model</h3>
                        <div class="code-block">
Relationships:
- belongsTo(Teams::class, 'team_id')
- belongsTo(Driver::class, 'driver_id')
- belongsTo(Task::class, 'task_id')

Methods:
- getPerformanceGradeAttribute() - Get performance grade
- scopeByTeam($query, $teamId) - Filter by team
- scopeByDriver($query, $driverId) - Filter by driver
- scopeByDateRange($query, $from, $to) - Filter by date range
                        </div>
                    </div>
                </div>

                <!-- Views Tab -->
                <div class="tab-pane fade" id="views">
                    <div class="section-card">
                        <h3 class="section-title">Management Views</h3>
                        <ul class="feature-list">
                            <li><strong>index.blade.php:</strong> Main dashboard with statistics cards, charts, and quick actions</li>
                            <li><strong>drivers.blade.php:</strong> Interactive drivers table with status management and filtering</li>
                            <li><strong>tasks.blade.php:</strong> Team tasks management with advanced filtering and status updates</li>
                        </ul>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">Assignment Views</h3>
                        <ul class="feature-list">
                            <li><strong>index.blade.php:</strong> Smart task assignment interface with criteria-based filtering</li>
                        </ul>
                    </div>

                    <div class="section-card">
                        <h3 class="section-title">Performance Views</h3>
                        <ul class="feature-list">
                            <li><strong>index.blade.php:</strong> Comprehensive performance dashboard with charts and export options</li>
                        </ul>
                    </div>
                </div>

                <!-- JavaScript Tab -->
                <div class="tab-pane fade" id="javascript">
                    <div class="section-card">
                        <h3 class="section-title">JavaScript Files</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <h5>management.js</h5>
                                <div class="code-block">
- Dashboard charts initialization
- Settings form handling
- Statistics updates
- Activity feed management
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>drivers.js</h5>
                                <div class="code-block">
- DataTable initialization
- Status update handling
- Bulk operations
- Performance metrics display
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <h5>assignment.js</h5>
                                <div class="code-block">
- Task filtering and search
- Assignment form handling
- Auto-assignment triggers
- Driver selection logic
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>tasks.js</h5>
                                <div class="code-block">
- Tasks table management
- Status update modals
- Export functionality
- Statistics calculation
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <h5>performance.js</h5>
                                <div class="code-block">
- ApexCharts initialization
- Performance data visualization
- Export report handling
- Filter management
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table('tasks_ads', function (Blueprint $table) {
      $table->boolean('included')->default(false)->after('lowest_price');
      $table->timestamp('closed_at')->nullable()->after('status');
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table('tasks_ads', function (Blueprint $table) {
      $table->dropColumn('included');
      $table->dropColumn('closed_at');
    });
  }
};
